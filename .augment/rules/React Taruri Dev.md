---
type: "manual"
---


## Tauri TypeScript & React Development Workflow Rules 

Adhere to the following 12 mandatory development workflow rules throughout the entire Tauri application development process. These rules build upon our established design system and existing project structure, emphasizing cross-language interaction, native desktop considerations, and efficient development.

-----

### Rule 1: Interactive Feedback Protocol

**ALWAYS** use the `interactive_feedback_mcp-feedback-enhanced` tool after completing any significant development milestone (component implementation, screen/view completion, feature addition, or bug fixes). This includes tasks spanning both frontend (TypeScript/React) and backend (Rust) changes.

  * Provide comprehensive summaries including: specific work completed, current implementation status, files modified (across both `src-tauri` and frontend), next planned steps, and any technical blockers encountered.
  * **WAIT** for explicit user approval before proceeding with major architectural changes, new feature implementations, or design system modifications.
  * Include the absolute project directory path (e.g., `/Users/<USER>/Documents/Code/Robot/pocket-manus`) and detailed progress summaries in all feedback requests.
  * Set appropriate timeout (default: 600 seconds) for user response.

-----

### Rule 2: Tauri Process Management

**BEFORE** starting any new Tauri development server, **ALWAYS** check for existing running processes using `lsof -i:<PORT>` or similar commands for the frontend development server and `ps aux | grep tauri` for the Rust backend via the `launch-process` tool.

  * **ONLY** launch new Tauri server using `npm run tauri dev` (or `yarn tauri dev`, `pnpm tauri dev`) if no existing process is found or if the existing process has terminated.
  * Prevent port conflicts and maintain development session continuity for both frontend and backend.

-----

### Rule 3: Library & Tauri API Context Acquisition

**ALWAYS** use `resolve-library-id_context7-mcp` to obtain proper Context7-compatible library IDs before using `get-library-docs_context7-mcp`.

  * Focus documentation requests on specific topics relevant to the current task (e.g., 'React hooks', 'Tauri API', 'Rust commands', 'file system access', 'native dialogs').
  * Set appropriate token limits (default: 10000) based on complexity of the feature being implemented.
  * Use library documentation (for React libraries like `react-native-reanimated`, Gluestack UI, etc.) **AND** Tauri's official documentation for both its JavaScript API and Rust API to understand proper usage patterns before implementing features with external libraries or Tauri's native capabilities.

-----

### Rule 4: Planning and Implementation with Serena MCP

**ALWAYS** use Serena MCP tools for all code planning, analysis, and implementation tasks.

  * Call `initial_instructions_serena` at the beginning of each development session.
  * Use `think_about_task_adherence_serena` before making any code changes to ensure alignment with project goals, considering both frontend and backend implications.
  * Save important context, progress updates, and implementation decisions using `write_memory_serena` with descriptive memory names.
  * Use Serena's symbolic editing tools (`replace_symbol_body_serena`, `insert_after_symbol_serena`, `find_symbol_serena`) for precise code modifications in both TypeScript and Rust files instead of manual file editing.
  * Call `think_about_whether_you_are_done_serena` before completing tasks.

-----

### Rule 5: Code Quality, Type Safety & Rust Safety Assurance

**ALWAYS** run TypeScript compilation check using `npx tsc --noEmit --skipLibCheck` via `launch-process` after making any code changes to React components. **Additionally, run Rust linter and check (`cargo clippy`, `cargo check`) for backend changes.**

  * Fix **ALL** TypeScript errors, type compatibility issues, missing property definitions, and import/export problems before proceeding to next steps.
  * Ensure all React components have proper TypeScript interfaces and default exports.
  * Verify that Gluestack UI component props match their expected TypeScript definitions.
  * Use diagnostics tool to check for IDE-reported issues in modified files.
  * **CONTINUOUSLY** view Tauri development server and Rust compilation outputs using `tail -n 20 running_outputs.log`.
  * **IMMEDIATELY** address any runtime errors, bundler warnings, or Rust compilation errors/warnings that appear in development logs.

-----

### Rule 6: UI Design Consistency & Component Alignment

**ALWAYS** reference the HTML design files in the `design/` folder (especially `design/pages/welcome.html` and `design/styles/design-system.css`) for visual fidelity targets.

  * **PRIORITIZE** reusing pre-defined design tokens from our established system (candy colors, glass morphism, typography, spacing).
  * Reuse optimized UI components from `components/ui/` directory (Button, Text, Box, VStack, HStack) with proper action variants.
  * Follow the `COMPONENT_ALIGNMENT_STRATEGY.md` for consistent component usage patterns.
  * Prefer Tailwind CSS than Styles; use `StyleSheet.create` to create static styles to avoid unnecessary re-rendering when you have to use Styles.
  * **VERIFY UI implementation alignment** using `#browser-tools-mcp` for desktop UI rendering accuracy.

-----

### Rule 7: Tauri Development Monitoring & Debugging

**TRACK** bundle compilation status, view loading performance, and component rendering errors specific to the Tauri environment.

  * **RESPOND promptly** to TypeScript compilation errors, missing dependency warnings, and Rust compilation/runtime errors.
  * **DEBUG web application using `Debugger Mode` in `#browser-tools-mcp` in the following sequence:**
    1.  Inspect relevant elements to identify the associated JavaScript files.
    2.  Set breakpoints at critical functions or state changes.
    3.  Step through code execution to trace logic and identify issues.
    4.  Monitor console output for errors and warnings.
  * **CHECK best practices** using `Audit Mode` in `#browser-tools-mcp` to identify performance, accessibility, SEO, and PWA improvements relevant to the Tauri context.
  * Kill process using `#desktop-commander kill_process` with appropriate process ID to terminate the process temporarily while it’s in conflict with another upcoming command.

-----

### Rule 8: Inter-Process Communication (IPC) Standards

**ALWAYS** define and use clear, type-safe communication patterns between the frontend (TypeScript/React) and the backend (Rust) via Tauri's IPC mechanism.

  * **Define Rust commands** (`#[tauri::command]`) for all backend functionalities intended for frontend consumption.
  * **Generate and use TypeScript bindings** for Rust commands to ensure type safety in frontend calls.
  * **Handle results and errors explicitly** across the IPC boundary, using Rust's `Result` type and TypeScript's error handling.
  * **Minimize the number and frequency of IPC calls** by batching requests or performing complex logic on the appropriate side (Rust for heavy computation, TS for UI logic).
  * For events from Rust to TypeScript, define and use Tauri's event system with clear event names and payload schemas.

-----

### Rule 9: Window Management & Desktop Experience

**MANAGE** window behavior and provide a cohesive desktop application experience instead of a mobile "safe area" approach.

  * Utilize Tauri's Window API to control window state (e.g., minimize, maximize, close, resize, titlebar customization).
  * Implement platform-specific UI adjustments for optimal desktop usability (e.g., menu bars, context menus, native dialogs).
  * **AVOID** hardcoding window sizes; consider user preferences and screen resolutions.
  * Test application behavior on different operating systems (Windows, macOS, Linux) to ensure consistent desktop experience.

-----

### Rule 10: Performance Optimization & Resource Management

**MINIMIZE** excessive `useState` and `useEffect` usage; prefer React Context and `useReducer` for complex state management.

  * Use Tauri's performance advantages (Rust backend, smaller bundle size) by offloading heavy computation and file system operations to the Rust side.
  * **OPTIMIZE IPC calls** to minimize latency and data transfer overhead.
  * **OPTIMIZE images**: use WebP format where supported, include explicit size dimensions, implement lazy loading.
  * Implement code splitting and lazy loading for non-critical frontend components using React's Suspense and dynamic imports.
  * PROFILE performance using browser developer tools and Tauri's built-in debugging features.
  * Prevent unnecessary re-renders by memoizing components with React.memo and using `useMemo`/`useCallback` appropriately.
  * Use React `startTransition`/`useTransition` for non-urgent state updates that don't block user interactions.
  * Leverage `@tanstack/react-query` for efficient data fetching, caching, and background updates, especially for data coming from the Rust backend.

-----

### Rule 11: State Management Architecture

Use Zod to declare / define any state appearing in App, for example:

```typescript
const CameraStateSchema = z.object({
  activeTab: z.enum(['camera', 'upload', 'gallery']),
  facing: z.enum(['back', 'front']),
  flash: z.enum(['off', 'on']),
  isCapturing: z.boolean(),
  hasPermission: z.boolean().nullable(),
});
type ICameraState = z.infer<typeof CameraStateSchema>;
const [hasPermission, setHasPermission] = useState<ICameraState['hasPermission']>(null);
```

  * Use React Context API for managing global application state (user preferences, theme, authentication) within the frontend.
  * Implement React Suspense + `use` hook pattern for declarative loading state presentation.
  * Use React `startTransition`/`useTransition` to optimize state updates and prevent UI blocking.
  * For complex state logic, consider Zustand for lightweight, TypeScript-friendly state management.
  * **Define clear patterns for synchronizing state or data between the Rust backend and the React frontend**, potentially using Tauri's event system or a shared state manager that can bridge the IPC.
  * Maintain state immutability and avoid direct mutations.

-----

### Rule 12: Validation and Error Handling (Cross-Boundary)

Use Zod for runtime type validation, API response validation, and form input validation on the TypeScript frontend. **Additionally, ensure robust error handling in the Rust backend.**

  * **PRIORITIZE** comprehensive error handling and edge case management across both frontend and backend:
      * Handle error conditions at the beginning of functions using early returns.
      * Use early return pattern to avoid deeply nested conditional statements.
      * Avoid unnecessary `else` statements; prefer `if-return` pattern for cleaner code flow.
  * Implement global error boundaries using React Error Boundary components to catch unexpected frontend errors.
  * **Ensure all IPC calls between TypeScript and Rust are wrapped with robust error handling**, gracefully managing potential failures from either side.
  * Provide user-friendly error messages and recovery options.
  * Log errors appropriately for debugging while protecting user privacy (ensure both frontend and backend logs are captured).

-----