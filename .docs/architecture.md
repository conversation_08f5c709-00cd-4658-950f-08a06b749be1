# Claudia Architecture Documentation

## Overview

Claudia is a powerful desktop application built with **Tauri 2** and **React 18** that provides a comprehensive GUI for managing Claude Code sessions, creating custom agents, and tracking usage analytics. The application follows a modern hybrid architecture combining a React frontend with a Rust backend.

## High-Level Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React 18 + TypeScript UI]
        Components[UI Components]
        API[API Client Layer]
    end
    
    subgraph "Tauri Bridge"
        Commands[Tauri Commands]
        Events[Event System]
        IPC[Inter-Process Communication]
    end
    
    subgraph "Backend Layer"
        Rust[Rust Backend]
        DB[(SQLite Database)]
        FS[File System Access]
        Processes[Process Management]
    end
    
    subgraph "External Systems"
        Claude[Claude Code CLI]
        MCP[MCP Servers]
        GitHub[GitHub API]
        FileSystem[Local File System]
    end
    
    UI --> API
    API --> Commands
    Commands --> Rust
    Rust --> DB
    Rust --> FS
    Rust --> Processes
    Rust --> Claude
    Rust --> MCP
    Rust --> GitHub
    Rust --> FileSystem
    
    Events --> UI
    Processes --> Events
```

## Technology Stack

### Frontend
- **React 18** - Modern React with hooks and concurrent features
- **TypeScript** - Type-safe JavaScript development
- **Vite 6** - Fast build tool and development server
- **Tailwind CSS v4** - Utility-first CSS framework
- **shadcn/ui** - High-quality UI component library
- **Framer Motion** - Animation library for smooth interactions
- **Radix UI** - Accessible component primitives

### Backend
- **Rust** - Systems programming language for performance and safety
- **Tauri 2** - Secure framework for building desktop applications
- **SQLite** (via rusqlite) - Embedded database for local data storage
- **Tokio** - Asynchronous runtime for Rust
- **Serde** - Serialization/deserialization framework

### Package Management
- **Bun** - Fast JavaScript runtime and package manager

## Core Modules

### 1. Frontend Architecture

#### Main Application (`src/App.tsx`)
- Central application state management
- View routing and navigation
- Global error handling
- Integration with all major components

#### Key Components
- **ProjectList** - Browse and manage Claude Code projects
- **SessionList** - View and resume coding sessions
- **CCAgents** - Agent creation and management interface
- **ClaudeCodeSession** - Interactive Claude Code session interface
- **UsageDashboard** - Analytics and cost tracking
- **MCPManager** - MCP server configuration and management
- **TimelineNavigator** - Checkpoint and session timeline visualization

#### API Client (`src/lib/api.ts`)
- Centralized interface to Tauri backend commands
- Type-safe API calls with TypeScript interfaces
- Error handling and response processing
- Real-time event handling for streaming operations

### 2. Backend Architecture

#### Command Modules (`src-tauri/src/commands/`)

**Agents Module (`agents.rs`)**
- Agent CRUD operations
- Agent execution and process management
- GitHub integration for agent import/export
- Real-time metrics and output streaming
- SQLite database operations for agent storage

**Claude Module (`claude.rs`)**
- Claude Code CLI integration
- Session management and execution
- Checkpoint creation and restoration
- Timeline navigation and forking
- File system operations and project scanning

**MCP Module (`mcp.rs`)**
- Model Context Protocol server management
- Server configuration and testing
- Integration with Claude Desktop configurations
- Project-specific MCP configurations

**Usage Module (`usage.rs`)**
- Token usage tracking and analytics
- Cost calculation across different models
- Session statistics and reporting
- JSONL parsing for usage data extraction

#### Core Systems

**Checkpoint System (`src-tauri/src/checkpoint/`)**
- Session state management and versioning
- File snapshot creation and restoration
- Timeline visualization and navigation
- Automatic checkpoint strategies
- Fork and branch management

**Process Management (`src-tauri/src/process/`)**
- Multi-process execution tracking
- Agent and Claude session monitoring
- Real-time output streaming
- Process lifecycle management
- Resource cleanup and error handling

## Data Flow Architecture

### 1. User Interaction Flow
```mermaid
sequenceDiagram
    participant User
    participant React
    participant API
    participant Tauri
    participant Rust
    participant External
    
    User->>React: Interact with UI
    React->>API: Call API method
    API->>Tauri: Invoke command
    Tauri->>Rust: Execute handler
    Rust->>External: Call external system
    External-->>Rust: Return result
    Rust-->>Tauri: Return response
    Tauri-->>API: Send result
    API-->>React: Update state
    React-->>User: Update UI
```

### 2. Agent Execution Flow
```mermaid
sequenceDiagram
    participant UI as React UI
    participant API as API Client
    participant CMD as Agent Commands
    participant DB as SQLite DB
    participant PROC as Process Registry
    participant CLAUDE as Claude CLI
    
    UI->>API: executeAgent(id, path, task)
    API->>CMD: execute_agent command
    CMD->>DB: Create agent run record
    CMD->>CLAUDE: Spawn claude process
    CLAUDE-->>CMD: Process started
    CMD->>PROC: Register process
    CMD->>DB: Update run status
    CMD-->>API: Return run ID
    
    loop Streaming Output
        CLAUDE->>CMD: Stream JSONL output
        CMD->>UI: Emit stream events
        CMD->>DB: Update metrics
    end
    
    CLAUDE-->>CMD: Process completed
    CMD->>DB: Update final status
    CMD->>PROC: Cleanup process
```

### 3. Checkpoint Management Flow
```mermaid
sequenceDiagram
    participant UI as Timeline UI
    participant API as API Client
    participant CHKPT as Checkpoint Manager
    participant STORAGE as Checkpoint Storage
    participant FS as File System
    
    UI->>API: createCheckpoint(description)
    API->>CHKPT: Create checkpoint
    CHKPT->>FS: Scan project files
    FS-->>CHKPT: File snapshots
    CHKPT->>STORAGE: Save checkpoint data
    STORAGE->>FS: Write checkpoint files
    CHKPT->>STORAGE: Update timeline
    STORAGE-->>CHKPT: Checkpoint saved
    CHKPT-->>API: Return checkpoint
    API-->>UI: Update timeline view
```

## Security Model

### Process Isolation
- Agents run in separate processes for security and stability
- Each process has controlled access to file system resources
- Process registry tracks and manages all running operations

### Permission Control
- Configurable file and network access per agent
- Sandboxed execution environment
- No telemetry or data collection

### Data Privacy
- All data stored locally on user's machine
- No external data transmission except for configured integrations
- Open source transparency for security auditing

## Integration Points

### Claude Code CLI
- Direct integration with official Claude Code command-line tool
- Streaming JSONL output processing
- Session management and continuation
- Model selection and configuration

### MCP (Model Context Protocol)
- Server discovery and configuration
- Integration with Claude Desktop settings
- Project-specific server configurations
- Connection testing and validation

### GitHub Integration
- Agent import/export from public repositories
- Community agent sharing
- Version control for agent configurations

### File System
- Project scanning and management
- CLAUDE.md file editing and preview
- Session history and checkpoint storage
- Usage analytics data persistence

## Performance Considerations

### Frontend Optimization
- Virtual scrolling for large lists
- Lazy loading of components
- Efficient state management
- Optimized re-rendering patterns

### Backend Optimization
- Asynchronous processing with Tokio
- Efficient SQLite queries and indexing
- Streaming data processing
- Memory-efficient file operations

### Process Management
- Resource cleanup and garbage collection
- Process monitoring and health checks
- Efficient inter-process communication
- Graceful shutdown handling

## Development Workflow

### Hot Reload Development
```bash
bun run tauri dev  # Start development with hot reload
```

### Production Build
```bash
bun run tauri build  # Create production executable
```

### Testing
```bash
cd src-tauri && cargo test  # Run Rust tests
bunx tsc --noEmit          # TypeScript type checking
```

This architecture provides a robust, scalable, and maintainable foundation for the Claudia application, enabling powerful Claude Code management capabilities while maintaining security and performance.
