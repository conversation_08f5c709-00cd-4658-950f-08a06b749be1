# Claudia Documentation

Welcome to the comprehensive documentation for <PERSON>, a powerful desktop application for managing Claude Code sessions, creating custom agents, and tracking usage analytics.

## 📚 Documentation Overview

This documentation provides detailed insights into <PERSON>'s architecture, components, data flows, and development practices. Whether you're a new developer joining the project or an experienced contributor, these documents will help you understand and work with the codebase effectively.

## 📋 Table of Contents

### Core Documentation

1. **[Architecture Overview](./architecture.md)**
   - High-level system architecture
   - Technology stack and design decisions
   - Core modules and their responsibilities
   - Security model and integration points

2. **[System Overview](./system-overview.md)**
   - Complete system architecture diagrams
   - Feature flows and process management
   - Data storage architecture
   - Performance characteristics

3. **[Component Architecture](./components.md)**
   - React component hierarchy
   - Component responsibilities and relationships
   - State management patterns
   - Performance optimizations

4. **[Data Flow Documentation](./data-flow.md)**
   - Data flow patterns between layers
   - Real-time streaming architecture
   - Database operations and file system integration
   - Error handling and recovery patterns

5. **[Development Guide](./development-guide.md)**
   - Development environment setup
   - Code patterns and best practices
   - Testing strategies
   - Debugging and logging

## 🏗️ Architecture Summary

Claudia is built using a modern hybrid architecture:

### Frontend
- **React 18** with TypeScript for type safety
- **Vite 6** for fast development and building
- **Tailwind CSS v4** + **shadcn/ui** for consistent UI
- **Framer Motion** for smooth animations

### Backend
- **Rust** with **Tauri 2** for secure desktop app framework
- **SQLite** for local data persistence
- **Tokio** for asynchronous operations
- **Serde** for serialization/deserialization

### Key Features
- **CC Agents**: Custom AI agents with specialized prompts
- **Project Management**: Visual project and session browser
- **Interactive Sessions**: Real-time Claude Code integration
- **Timeline Navigation**: Checkpoint system with branching
- **Usage Analytics**: Cost tracking and token analytics
- **MCP Integration**: Model Context Protocol server management

## 🔄 Main Data Flows

### 1. Agent Execution Flow
```
User Input → React UI → API Client → Tauri Commands → Rust Backend → Claude CLI → Streaming Output → Real-time UI Updates
```

### 2. Session Management Flow
```
Project Selection → Session Creation → Claude Code Execution → Checkpoint Creation → Timeline Updates → File State Management
```

### 3. Real-time Communication
```
Backend Events → Tauri Event System → React Event Listeners → State Updates → UI Re-rendering
```

## 🧩 Component Hierarchy

```
App (Root)
├── Topbar (Navigation)
├── ProjectList (Project Browser)
├── SessionList (Session Management)
├── CCAgents (Agent Management)
│   ├── CreateAgent (Agent Creation)
│   ├── AgentRunsList (Execution History)
│   └── GitHubAgentBrowser (Import Agents)
├── ClaudeCodeSession (Interactive Sessions)
│   ├── TimelineNavigator (Checkpoint Navigation)
│   ├── FloatingPromptInput (Message Input)
│   └── StreamMessage (Message Display)
├── UsageDashboard (Analytics)
├── MCPManager (MCP Configuration)
└── Settings (Application Settings)
```

## 🔧 Development Quick Start

### Prerequisites
```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install Bun
curl -fsSL https://bun.sh/install | bash

# Install Claude Code CLI
# Download from https://claude.ai/code
```

### Setup and Run
```bash
# Clone repository
git clone https://github.com/getAsterisk/claudia.git
cd claudia

# Install dependencies
bun install

# Start development server
bun run tauri dev

# Build for production
bun run tauri build
```

### Development Commands
```bash
# Frontend only (for UI development)
bun run dev

# Type checking
bunx tsc --noEmit

# Run Rust tests
cd src-tauri && cargo test

# Format Rust code
cd src-tauri && cargo fmt
```

## 📊 Key Metrics and Performance

### Frontend Performance
- **Virtual scrolling** for large lists (agent runs, sessions)
- **React.memo** and **useMemo** for expensive computations
- **Lazy loading** for heavy components
- **Optimized re-rendering** with proper dependency arrays

### Backend Performance
- **Asynchronous processing** with Tokio runtime
- **Connection pooling** for database operations
- **Streaming data processing** for real-time updates
- **Efficient file operations** with async I/O

### Security Features
- **Process isolation** for agent execution
- **Sandboxed file system access**
- **No telemetry or data collection**
- **Local-only data storage**

## 🔐 Security Model

### Process Management
- Each agent runs in a separate process
- Process registry tracks all running operations
- Automatic cleanup on process termination
- Resource monitoring and limits

### Data Privacy
- All data stored locally on user's machine
- No external data transmission (except configured integrations)
- Open source for transparency and auditing
- User-controlled permissions for file and network access

## 🌐 Integration Points

### External Systems
- **Claude Code CLI**: Direct integration for AI-powered coding
- **GitHub API**: Agent import/export and community sharing
- **MCP Servers**: Model Context Protocol integration
- **File System**: Project scanning and management

### Configuration Management
- **SQLite database**: Agent storage and run history
- **File-based configs**: MCP server configurations
- **JSON exports**: Agent sharing and backup
- **Timeline storage**: Checkpoint and session data

## 🚀 Deployment and Distribution

### Build Targets
- **macOS**: Universal binary (Intel + Apple Silicon)
- **Windows**: MSI installer and portable executable
- **Linux**: AppImage, DEB package, and binary

### Release Process
```bash
# Create production build
bun run tauri build

# Artifacts location
# - macOS: src-tauri/target/release/bundle/macos/
# - Windows: src-tauri/target/release/bundle/msi/
# - Linux: src-tauri/target/release/bundle/appimage/
```

## 📈 Future Roadmap

### Planned Features
- **Multi-language support** (i18n)
- **Plugin system** for extensibility
- **Advanced analytics** with custom dashboards
- **Team collaboration** features
- **Cloud synchronization** (optional)

### Performance Improvements
- **Database optimization** with better indexing
- **Memory usage optimization** for large projects
- **Startup time improvements**
- **Background processing** enhancements

## 🤝 Contributing

### Getting Started
1. Read the [Development Guide](./development-guide.md)
2. Check the [Component Architecture](./components.md)
3. Understand the [Data Flow](./data-flow.md)
4. Follow the patterns in existing code

### Code Standards
- **TypeScript** for all frontend code
- **Rust** with proper error handling for backend
- **Consistent naming** conventions
- **Comprehensive testing** for new features
- **Documentation** for public APIs

### Pull Request Process
1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Update documentation if needed
5. Submit pull request with clear description

## 📞 Support and Resources

### Documentation
- **Architecture**: Understanding the system design
- **Components**: React component patterns
- **Data Flow**: Information flow patterns
- **Development**: Setup and coding guidelines

### Community
- **GitHub Issues**: Bug reports and feature requests
- **Discussions**: Community support and ideas
- **Discord**: Real-time developer chat (coming soon)

### External Resources
- **Tauri Documentation**: https://tauri.app/
- **React Documentation**: https://react.dev/
- **Rust Documentation**: https://doc.rust-lang.org/
- **Claude Code**: https://claude.ai/code

---

This documentation is maintained by the Claudia development team and community contributors. For questions or improvements, please open an issue or submit a pull request.
