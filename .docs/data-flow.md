# Claudia Data Flow Documentation

## Overview

This document details the data flow patterns within <PERSON>, showing how information moves between the React frontend, Tauri bridge, Rust backend, and external systems.

## Core Data Flow Patterns

### 1. Project and Session Management

```mermaid
flowchart TD
    A[User Opens Projects View] --> B[React Component Loads]
    B --> C[API Call: listProjects()]
    C --> D[Tauri Command: list_projects]
    D --> E[Rust: Scan ~/.claude/projects/]
    E --> F[File System Read]
    F --> G[Return Project List]
    G --> H[Update React State]
    H --> I[Render Project Cards]
    
    I --> J[User Selects Project]
    J --> K[API Call: getProjectSessions()]
    K --> L[Tauri Command: get_project_sessions]
    L --> M[Rust: Scan Session Directories]
    M --> N[Parse Session Metadata]
    N --> O[Return Session List]
    O --> P[Update Session View]
```

### 2. Agent Creation and Execution

```mermaid
flowchart TD
    A[User Creates Agent] --> B[Form Submission]
    B --> C[API Call: createAgent()]
    C --> D[Tauri Command: create_agent]
    D --> E[Rust: Validate Agent Data]
    E --> F[SQLite: Insert Agent Record]
    F --> G[Return Agent ID]
    G --> H[Update Agent List]
    
    H --> I[User Executes Agent]
    I --> J[API Call: executeAgent()]
    J --> K[Tauri Command: execute_agent]
    K --> L[Rust: Create Run Record]
    L --> M[SQLite: Insert Agent Run]
    M --> N[Spawn Claude Process]
    N --> O[Register in Process Registry]
    O --> P[Start Output Streaming]
    
    P --> Q[Claude Process Output]
    Q --> R[Parse JSONL Stream]
    R --> S[Emit Tauri Events]
    S --> T[React Event Listeners]
    T --> U[Update UI in Real-time]
```

### 3. Checkpoint and Timeline Management

```mermaid
flowchart TD
    A[User in Claude Session] --> B[Tool Usage Detected]
    B --> C[Auto-Checkpoint Trigger]
    C --> D[Checkpoint Manager]
    D --> E[Scan Project Files]
    E --> F[Create File Snapshots]
    F --> G[Calculate Diff]
    G --> H[Save to Storage]
    H --> I[Update Timeline]
    I --> J[Emit Timeline Event]
    J --> K[Update Timeline UI]
    
    K --> L[User Navigates Timeline]
    L --> M[Select Checkpoint]
    M --> N[API Call: restoreCheckpoint()]
    N --> O[Tauri Command: restore_checkpoint]
    O --> P[Load Checkpoint Data]
    P --> Q[Restore File States]
    Q --> R[Update Session State]
    R --> S[Refresh UI]
```

## Streaming Data Patterns

### Real-time Agent Output

```mermaid
sequenceDiagram
    participant UI as React UI
    participant Events as Tauri Events
    participant Stream as Output Stream
    participant Process as Claude Process
    
    UI->>Events: Subscribe to stream events
    Process->>Stream: Write JSONL output
    Stream->>Events: Parse and emit events
    Events->>UI: Real-time updates
    
    loop Continuous Streaming
        Process->>Stream: New output line
        Stream->>Events: agent_output event
        Events->>UI: Update message list
        UI->>UI: Scroll to bottom
    end
    
    Process->>Stream: Process completed
    Stream->>Events: agent_completed event
    Events->>UI: Update status
```

### Session Message Tracking

```mermaid
sequenceDiagram
    participant Session as Claude Session
    participant Manager as Checkpoint Manager
    participant Storage as File Storage
    participant Timeline as Timeline UI
    
    Session->>Manager: Track message
    Manager->>Manager: Parse JSONL
    Manager->>Manager: Detect tool usage
    
    alt Auto-checkpoint enabled
        Manager->>Storage: Create checkpoint
        Storage->>Timeline: Update timeline
        Timeline->>Timeline: Refresh view
    end
    
    Manager->>Manager: Update message index
    Manager->>Storage: Save session state
```

## Database Operations Flow

### Agent Management

```mermaid
flowchart LR
    A[Agent CRUD Operations] --> B{Operation Type}
    
    B -->|Create| C[Validate Input]
    B -->|Read| D[Query Database]
    B -->|Update| E[Validate Changes]
    B -->|Delete| F[Check Dependencies]
    
    C --> G[SQLite Insert]
    D --> H[SQLite Select]
    E --> I[SQLite Update]
    F --> J[SQLite Delete]
    
    G --> K[Return New Agent]
    H --> L[Return Agent Data]
    I --> M[Return Updated Agent]
    J --> N[Return Success]
```

### Usage Analytics Processing

```mermaid
flowchart TD
    A[Session Completes] --> B[Parse JSONL Files]
    B --> C[Extract Usage Data]
    C --> D[Calculate Costs]
    D --> E[Group by Model/Date]
    E --> F[Update Statistics]
    F --> G[Cache Results]
    
    G --> H[Dashboard Request]
    H --> I[Load Cached Data]
    I --> J[Generate Charts]
    J --> K[Return Analytics]
```

## File System Integration

### Project Scanning

```mermaid
flowchart TD
    A[Scan Request] --> B[Read ~/.claude/projects/]
    B --> C{Directory Exists?}
    C -->|No| D[Return Empty List]
    C -->|Yes| E[Iterate Directories]
    E --> F[Read Project Metadata]
    F --> G[Parse .claude/project.json]
    G --> H[Collect Session Info]
    H --> I[Return Project List]
```

### CLAUDE.md File Management

```mermaid
flowchart TD
    A[Find CLAUDE.md Files] --> B[Recursive Directory Scan]
    B --> C[Filter .md Files]
    C --> D[Check Filename Pattern]
    D --> E[Read File Content]
    E --> F[Parse Markdown]
    F --> G[Return File List]
    
    G --> H[User Edits File]
    H --> I[Save Changes]
    I --> J[Write to File System]
    J --> K[Update File Watcher]
    K --> L[Refresh UI]
```

## External System Integration

### Claude Code CLI Integration

```mermaid
sequenceDiagram
    participant App as Claudia App
    participant CLI as Claude CLI
    participant API as Claude API
    
    App->>CLI: Execute command
    CLI->>API: Send request
    API-->>CLI: Stream response
    CLI-->>App: JSONL output
    
    loop Process Stream
        CLI->>App: Output chunk
        App->>App: Parse JSONL
        App->>App: Update UI
    end
    
    CLI-->>App: Process complete
    App->>App: Final cleanup
```

### MCP Server Management

```mermaid
flowchart TD
    A[MCP Server Request] --> B[Load Configuration]
    B --> C[Parse Server Config]
    C --> D[Test Connection]
    D --> E{Connection OK?}
    E -->|Yes| F[Mark Active]
    E -->|No| G[Mark Error]
    F --> H[Save Status]
    G --> I[Log Error]
    H --> J[Update UI]
    I --> J
```

### GitHub Agent Import

```mermaid
sequenceDiagram
    participant UI as React UI
    participant API as GitHub API
    participant Parser as JSON Parser
    participant DB as SQLite DB
    
    UI->>API: Fetch agent list
    API-->>UI: Return file list
    UI->>API: Download agent file
    API-->>Parser: Raw JSON data
    Parser->>Parser: Validate format
    Parser->>DB: Insert agent
    DB-->>UI: Confirm import
```

## Error Handling Patterns

### Graceful Error Recovery

```mermaid
flowchart TD
    A[Operation Starts] --> B{Error Occurs?}
    B -->|No| C[Success Path]
    B -->|Yes| D[Catch Error]
    D --> E[Log Error Details]
    E --> F[Determine Error Type]
    F --> G{Recoverable?}
    G -->|Yes| H[Retry Operation]
    G -->|No| I[Show User Error]
    H --> J[Exponential Backoff]
    J --> A
    I --> K[Cleanup Resources]
```

### Process Failure Handling

```mermaid
sequenceDiagram
    participant Process as Claude Process
    participant Registry as Process Registry
    participant UI as User Interface
    participant Cleanup as Cleanup Service
    
    Process->>Registry: Process fails
    Registry->>UI: Notify failure
    Registry->>Cleanup: Trigger cleanup
    Cleanup->>Registry: Remove process
    Cleanup->>UI: Update status
    UI->>UI: Show error message
```

## Performance Optimization Patterns

### Lazy Loading and Caching

```mermaid
flowchart TD
    A[Component Mounts] --> B{Data in Cache?}
    B -->|Yes| C[Use Cached Data]
    B -->|No| D[Fetch from Backend]
    D --> E[Store in Cache]
    E --> F[Return Data]
    C --> G[Render Component]
    F --> G
    
    G --> H[User Scrolls]
    H --> I[Load More Items]
    I --> J[Virtual Scrolling]
    J --> K[Render Visible Items]
```

### Efficient State Updates

```mermaid
flowchart TD
    A[State Change] --> B[React State Update]
    B --> C[Component Re-render]
    C --> D[Diff Virtual DOM]
    D --> E[Update Real DOM]
    E --> F[Trigger Effects]
    F --> G[Update Dependent Components]
```

This data flow architecture ensures efficient, real-time communication between all system components while maintaining data consistency and providing a responsive user experience.
