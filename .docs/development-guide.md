# Claudia Development Guide

## Development Environment Setup

### Prerequisites
- **Rust** (1.70.0 or later)
- **Bun** (latest version)
- **Claude Code CLI** (installed and in PATH)
- **Git** for version control

### Quick Start
```bash
# Clone the repository
git clone https://github.com/getAsterisk/claudia.git
cd claudia

# Install dependencies
bun install

# Start development server
bun run tauri dev
```

## Project Structure

```
claudia/
├── src/                          # React frontend
│   ├── components/              # UI components
│   │   ├── ui/                 # Base UI components (shadcn/ui)
│   │   ├── CCAgents.tsx        # Agent management
│   │   ├── ClaudeCodeSession.tsx # Interactive sessions
│   │   └── ...                 # Feature components
│   ├── lib/                    # Utilities and API client
│   │   ├── api.ts             # Tauri command interface
│   │   └── utils.ts           # Helper functions
│   └── App.tsx                # Root component
├── src-tauri/                  # Rust backend
│   ├── src/
│   │   ├── commands/          # Tauri command handlers
│   │   │   ├── agents.rs      # Agent operations
│   │   │   ├── claude.rs      # Claude Code integration
│   │   │   ├── mcp.rs         # MCP server management
│   │   │   └── usage.rs       # Usage analytics
│   │   ├── checkpoint/        # Session checkpointing
│   │   ├── process/           # Process management
│   │   └── main.rs           # Application entry point
│   └── Cargo.toml            # Rust dependencies
├── public/                    # Static assets
└── package.json              # Node.js dependencies
```

## Development Patterns

### 1. Adding New Tauri Commands

#### Backend (Rust)
```rust
// src-tauri/src/commands/your_module.rs
use tauri::command;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
pub struct YourData {
    pub field1: String,
    pub field2: i32,
}

#[command]
pub async fn your_command(data: YourData) -> Result<String, String> {
    // Your logic here
    Ok("Success".to_string())
}
```

#### Register Command
```rust
// src-tauri/src/main.rs
.invoke_handler(tauri::generate_handler![
    // ... existing commands
    your_command,
])
```

#### Frontend (TypeScript)
```typescript
// src/lib/api.ts
export const api = {
  // ... existing methods
  
  async yourCommand(data: YourData): Promise<string> {
    try {
      return await invoke<string>("your_command", { data });
    } catch (error) {
      console.error("Failed to execute your command:", error);
      throw error;
    }
  },
};
```

### 2. Creating New React Components

#### Component Structure
```typescript
// src/components/YourComponent.tsx
import React, { useState, useEffect } from "react";
import { api } from "@/lib/api";
import { Button } from "@/components/ui/button";

interface YourComponentProps {
  data: SomeData;
  onAction: (result: ActionResult) => void;
  className?: string;
}

export const YourComponent: React.FC<YourComponentProps> = ({
  data,
  onAction,
  className
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAction = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await api.yourCommand(data);
      onAction(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={className}>
      {error && (
        <div className="text-red-500 mb-4">{error}</div>
      )}
      <Button 
        onClick={handleAction} 
        disabled={loading}
      >
        {loading ? "Loading..." : "Action"}
      </Button>
    </div>
  );
};
```

### 3. Real-time Event Handling

#### Backend Event Emission
```rust
// src-tauri/src/commands/your_module.rs
use tauri::{AppHandle, Manager};

#[command]
pub async fn start_process(app: AppHandle) -> Result<(), String> {
    // Start your process
    tokio::spawn(async move {
        loop {
            // Your process logic
            let data = get_process_data();
            
            // Emit event to frontend
            app.emit_all("process_update", data)
                .map_err(|e| format!("Failed to emit event: {}", e))?;
                
            tokio::time::sleep(Duration::from_secs(1)).await;
        }
    });
    
    Ok(())
}
```

#### Frontend Event Listening
```typescript
// src/components/YourComponent.tsx
import { listen, type UnlistenFn } from "@tauri-apps/api/event";

export const YourComponent: React.FC = () => {
  const [processData, setProcessData] = useState<ProcessData | null>(null);

  useEffect(() => {
    let unlisten: UnlistenFn;

    const setupListener = async () => {
      unlisten = await listen<ProcessData>('process_update', (event) => {
        setProcessData(event.payload);
      });
    };

    setupListener();

    return () => {
      if (unlisten) {
        unlisten();
      }
    };
  }, []);

  return (
    <div>
      {processData && (
        <div>Process Status: {processData.status}</div>
      )}
    </div>
  );
};
```

### 4. Database Operations

#### SQLite Schema Management
```rust
// src-tauri/src/commands/your_module.rs
use rusqlite::{Connection, params};

pub fn init_your_table(conn: &Connection) -> Result<(), rusqlite::Error> {
    conn.execute(
        "CREATE TABLE IF NOT EXISTS your_table (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            data TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )",
        [],
    )?;
    Ok(())
}

#[command]
pub async fn create_record(
    db: State<'_, YourDb>,
    name: String,
    data: String,
) -> Result<i64, String> {
    let conn = db.0.lock().map_err(|e| e.to_string())?;
    
    conn.execute(
        "INSERT INTO your_table (name, data) VALUES (?1, ?2)",
        params![name, data],
    ).map_err(|e| e.to_string())?;
    
    Ok(conn.last_insert_rowid())
}
```

### 5. Error Handling Patterns

#### Backend Error Handling
```rust
// src-tauri/src/commands/your_module.rs
use anyhow::{Context, Result};
use log::{error, info};

#[command]
pub async fn risky_operation(input: String) -> Result<String, String> {
    perform_operation(input)
        .await
        .map_err(|e| {
            error!("Operation failed: {}", e);
            format!("Failed to perform operation: {}", e)
        })
}

async fn perform_operation(input: String) -> Result<String> {
    // Your operation that might fail
    let result = some_fallible_operation(&input)
        .context("Failed to process input")?;
    
    info!("Operation completed successfully");
    Ok(result)
}
```

#### Frontend Error Handling
```typescript
// src/components/YourComponent.tsx
import { useState } from "react";
import { toast } from "@/components/ui/toast";

export const YourComponent: React.FC = () => {
  const [error, setError] = useState<string | null>(null);

  const handleOperation = async () => {
    try {
      setError(null);
      await api.riskyOperation(input);
      toast.success("Operation completed successfully");
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      toast.error(`Operation failed: ${errorMessage}`);
    }
  };

  return (
    <div>
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}
      {/* Your component content */}
    </div>
  );
};
```

## Testing Strategies

### Backend Testing
```rust
// src-tauri/src/commands/tests.rs
#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_your_command() {
        let temp_dir = tempdir().unwrap();
        let db_path = temp_dir.path().join("test.db");
        
        // Setup test database
        let conn = Connection::open(&db_path).unwrap();
        init_your_table(&conn).unwrap();
        
        // Test your command
        let result = your_command(test_data).await;
        assert!(result.is_ok());
    }
}
```

### Frontend Testing
```typescript
// src/components/__tests__/YourComponent.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { YourComponent } from '../YourComponent';

// Mock the API
jest.mock('@/lib/api', () => ({
  api: {
    yourCommand: jest.fn(),
  },
}));

describe('YourComponent', () => {
  it('should handle successful operation', async () => {
    const mockOnAction = jest.fn();
    const mockApi = require('@/lib/api').api;
    mockApi.yourCommand.mockResolvedValue('success');

    render(<YourComponent data={testData} onAction={mockOnAction} />);
    
    fireEvent.click(screen.getByText('Action'));
    
    await waitFor(() => {
      expect(mockOnAction).toHaveBeenCalledWith('success');
    });
  });
});
```

## Performance Best Practices

### Frontend Optimization
```typescript
// Use React.memo for expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{/* Expensive rendering */}</div>;
});

// Use useMemo for expensive calculations
const processedData = useMemo(() => {
  return expensiveProcessing(rawData);
}, [rawData]);

// Use useCallback for stable function references
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);

// Virtual scrolling for large lists
import { useVirtualizer } from '@tanstack/react-virtual';

const VirtualList = ({ items }) => {
  const parentRef = useRef();
  
  const virtualizer = useVirtualizer({
    count: items.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 50,
  });

  return (
    <div ref={parentRef} style={{ height: '400px', overflow: 'auto' }}>
      {virtualizer.getVirtualItems().map((virtualItem) => (
        <div key={virtualItem.key} style={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: `${virtualItem.size}px`,
          transform: `translateY(${virtualItem.start}px)`,
        }}>
          {items[virtualItem.index]}
        </div>
      ))}
    </div>
  );
};
```

### Backend Optimization
```rust
// Use connection pooling for database operations
use std::sync::Arc;
use tokio::sync::Mutex;

pub struct DatabasePool {
    connections: Arc<Mutex<Vec<Connection>>>,
}

// Async operations with proper error handling
use tokio::time::{timeout, Duration};

async fn with_timeout<T>(
    operation: impl Future<Output = Result<T, String>>,
) -> Result<T, String> {
    timeout(Duration::from_secs(30), operation)
        .await
        .map_err(|_| "Operation timed out".to_string())?
}

// Efficient file operations
use tokio::fs;
use tokio::io::{AsyncReadExt, AsyncWriteExt};

async fn read_file_efficiently(path: &Path) -> Result<String, std::io::Error> {
    let mut file = fs::File::open(path).await?;
    let mut contents = String::new();
    file.read_to_string(&mut contents).await?;
    Ok(contents)
}
```

## Debugging and Logging

### Backend Logging
```rust
// src-tauri/src/main.rs
use log::{debug, info, warn, error};

fn main() {
    env_logger::init();
    
    info!("Starting Claudia application");
    // ... rest of main
}

// In your commands
#[command]
pub async fn your_command(input: String) -> Result<String, String> {
    debug!("Received input: {}", input);
    
    match process_input(&input).await {
        Ok(result) => {
            info!("Command completed successfully");
            Ok(result)
        }
        Err(e) => {
            error!("Command failed: {}", e);
            Err(e.to_string())
        }
    }
}
```

### Frontend Debugging
```typescript
// src/lib/debug.ts
export const debug = {
  log: (message: string, data?: any) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[Claudia] ${message}`, data);
    }
  },
  
  error: (message: string, error?: any) => {
    console.error(`[Claudia Error] ${message}`, error);
  },
  
  time: (label: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.time(`[Claudia] ${label}`);
    }
  },
  
  timeEnd: (label: string) => {
    if (process.env.NODE_ENV === 'development') {
      console.timeEnd(`[Claudia] ${label}`);
    }
  },
};

// Usage in components
import { debug } from '@/lib/debug';

const YourComponent = () => {
  useEffect(() => {
    debug.time('Component Mount');
    
    return () => {
      debug.timeEnd('Component Mount');
    };
  }, []);
  
  const handleAction = async () => {
    debug.log('Action started', { data });
    try {
      const result = await api.yourCommand(data);
      debug.log('Action completed', { result });
    } catch (error) {
      debug.error('Action failed', error);
    }
  };
};
```

This development guide provides the essential patterns and practices for contributing to Claudia, ensuring consistency and maintainability across the codebase.
