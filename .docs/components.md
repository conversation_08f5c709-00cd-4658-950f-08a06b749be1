# Claudia Component Architecture

## Overview

This document provides a comprehensive overview of the React component hierarchy and architecture in Claudia, detailing the relationships, responsibilities, and data flow between components.

## Component Hierarchy

```mermaid
graph TD
    App[App.tsx - Root Component]
    
    App --> Topbar[Topbar - Navigation & Actions]
    App --> ProjectList[ProjectList - Project Browser]
    App --> SessionList[SessionList - Session Management]
    App --> CCAgents[CCAgents - Agent Management]
    App --> ClaudeCodeSession[ClaudeCodeSession - Interactive Sessions]
    App --> UsageDashboard[UsageDashboard - Analytics]
    App --> MCPManager[MCPManager - MCP Configuration]
    App --> Settings[Settings - Application Settings]
    
    CCAgents --> CreateAgent[CreateAgent - Agent Creation Form]
    CCAgents --> AgentRunsList[AgentRunsList - Execution History]
    CCAgents --> GitHubAgentBrowser[GitHubAgentBrowser - Import Agents]
    
    ClaudeCodeSession --> TimelineNavigator[TimelineNavigator - Checkpoint Navigation]
    ClaudeCodeSession --> FloatingPromptInput[FloatingPromptInput - Message Input]
    ClaudeCodeSession --> StreamMessage[StreamMessage - Message Display]
    ClaudeCodeSession --> WebviewPreview[WebviewPreview - Web Content]
    
    AgentRunsList --> AgentRunView[AgentRunView - Individual Run Details]
    AgentRunView --> AgentExecution[AgentExecution - Real-time Execution]
    
    MCPManager --> MCPServerList[MCPServerList - Server Management]
    MCPManager --> MCPAddServer[MCPAddServer - Add New Server]
    
    UsageDashboard --> TokenCounter[TokenCounter - Usage Metrics]
    
    TimelineNavigator --> CheckpointSettings[CheckpointSettings - Timeline Config]
```

## Core Components

### 1. Application Root (`App.tsx`)

**Responsibilities:**
- Central state management and view routing
- Global error boundary and loading states
- Integration with all major feature components
- Theme and context providers

**Key State:**
```typescript
type View = "welcome" | "projects" | "agents" | "editor" | "settings" | 
           "claude-file-editor" | "claude-code-session" | "usage-dashboard" | "mcp";

interface AppState {
  currentView: View;
  selectedProject: Project | null;
  selectedSession: Session | null;
  isLoading: boolean;
  error: string | null;
}
```

**Data Flow:**
- Manages navigation between different application views
- Provides shared state to child components
- Handles global error states and loading indicators

### 2. Project Management Components

#### ProjectList (`ProjectList.tsx`)
**Purpose:** Browse and manage Claude Code projects from `~/.claude/projects/`

**Features:**
- Project discovery and listing
- Search and filtering capabilities
- Project metadata display
- Quick actions (open, settings)

**Data Sources:**
- `api.listProjects()` - Fetches project list from backend
- Local state for search and filtering

#### SessionList (`SessionList.tsx`)
**Purpose:** Display and manage sessions within a selected project

**Features:**
- Session history visualization
- Session metadata (first message, timestamp)
- Resume/continue session actions
- Session search and sorting

**Integration:**
- Receives project context from parent
- Communicates with `ClaudeCodeSession` for session resumption

### 3. Agent Management System

#### CCAgents (`CCAgents.tsx`)
**Purpose:** Main interface for agent management and execution

**Key Features:**
- Agent library browsing
- Agent creation and editing
- Execution management
- Import/export functionality

**Child Components:**
- `CreateAgent` - Agent creation form
- `AgentRunsList` - Execution history
- `GitHubAgentBrowser` - Community agent import

#### CreateAgent (`CreateAgent.tsx`)
**Purpose:** Form interface for creating and editing agents

**Form Fields:**
```typescript
interface AgentForm {
  name: string;
  icon: string;
  systemPrompt: string;
  defaultTask?: string;
  model: string;
}
```

**Validation:**
- Required field validation
- System prompt length limits
- Model availability checking

#### AgentExecution (`AgentExecution.tsx`)
**Purpose:** Real-time agent execution monitoring

**Features:**
- Live output streaming
- Process status monitoring
- Execution controls (pause, stop, restart)
- Metrics display (tokens, cost, duration)

**Real-time Updates:**
```typescript
// Event listener for streaming output
useEffect(() => {
  const unlisten = listen('agent_output', (event) => {
    const message = event.payload as ClaudeStreamMessage;
    setMessages(prev => [...prev, message]);
  });
  
  return unlisten;
}, []);
```

### 4. Interactive Session Components

#### ClaudeCodeSession (`ClaudeCodeSession.tsx`)
**Purpose:** Interactive Claude Code session interface

**Key Features:**
- Real-time message streaming
- Checkpoint management integration
- Multi-pane layout (chat, timeline, preview)
- Session state persistence

**State Management:**
```typescript
interface SessionState {
  messages: ClaudeStreamMessage[];
  isLoading: boolean;
  projectPath: string;
  currentSession: Session | null;
  checkpointVersion: number;
}
```

#### TimelineNavigator (`TimelineNavigator.tsx`)
**Purpose:** Visual checkpoint and timeline management

**Features:**
- Branching timeline visualization
- Checkpoint creation and restoration
- Diff viewing between checkpoints
- Fork management

**Timeline Structure:**
```typescript
interface TimelineNode {
  checkpoint: Checkpoint;
  children: TimelineNode[];
  isExpanded: boolean;
  depth: number;
}
```

#### StreamMessage (`StreamMessage.tsx`)
**Purpose:** Individual message rendering with syntax highlighting

**Message Types:**
- User messages
- Assistant responses
- Tool usage blocks
- Error messages
- System notifications

**Features:**
- Markdown rendering
- Code syntax highlighting
- Copy functionality
- Message timestamps

### 5. Analytics and Monitoring

#### UsageDashboard (`UsageDashboard.tsx`)
**Purpose:** Usage analytics and cost tracking

**Visualizations:**
- Cost trends over time
- Token usage by model
- Project-wise breakdown
- Session statistics

**Data Processing:**
```typescript
interface UsageStats {
  totalCost: number;
  totalTokens: number;
  byModel: ModelUsage[];
  byDate: DailyUsage[];
  byProject: ProjectUsage[];
}
```

#### TokenCounter (`TokenCounter.tsx`)
**Purpose:** Real-time token and cost tracking

**Features:**
- Live token counting
- Cost estimation
- Model-specific pricing
- Usage alerts

### 6. MCP Integration Components

#### MCPManager (`MCPManager.tsx`)
**Purpose:** Model Context Protocol server management

**Features:**
- Server discovery and configuration
- Connection testing
- Import from Claude Desktop
- Project-specific configurations

#### MCPServerList (`MCPServerList.tsx`)
**Purpose:** List and manage configured MCP servers

**Server Management:**
- Add/remove servers
- Test connections
- Enable/disable servers
- Configuration editing

### 7. UI Foundation Components

#### UI Components (`src/components/ui/`)
**Purpose:** Reusable UI primitives based on shadcn/ui

**Key Components:**
- `Button` - Interactive buttons with variants
- `Card` - Content containers
- `Dialog` - Modal dialogs
- `Input` - Form inputs
- `Select` - Dropdown selections
- `Tabs` - Tabbed interfaces
- `Toast` - Notification system

**Design System:**
- Consistent styling with Tailwind CSS
- Accessibility compliance
- Theme support (light/dark)
- Responsive design patterns

## Component Communication Patterns

### 1. Props Down, Events Up
```typescript
// Parent passes data down
<ChildComponent 
  data={parentData}
  onAction={handleChildAction}
/>

// Child emits events up
const handleClick = () => {
  onAction(childData);
};
```

### 2. Context for Shared State
```typescript
// Global state context
const AppContext = createContext<AppState>();

// Provider at app level
<AppContext.Provider value={appState}>
  <ComponentTree />
</AppContext.Provider>

// Consumption in components
const { currentView, setView } = useContext(AppContext);
```

### 3. Event-Driven Updates
```typescript
// Tauri event listeners for real-time updates
useEffect(() => {
  const unlistenOutput = listen('agent_output', handleOutput);
  const unlistenStatus = listen('agent_status', handleStatus);
  
  return () => {
    unlistenOutput();
    unlistenStatus();
  };
}, []);
```

## State Management Strategy

### Local Component State
- UI-specific state (form inputs, loading states)
- Component-specific data (expanded/collapsed states)
- Temporary state (search queries, filters)

### Shared Application State
- Current view and navigation state
- Selected project/session context
- Global settings and preferences
- User authentication state

### Backend State Synchronization
- Real-time data from Rust backend
- Database-persisted information
- File system state
- Process status and metrics

## Performance Optimizations

### Virtual Scrolling
```typescript
// Large lists use virtual scrolling
const virtualizer = useVirtualizer({
  count: items.length,
  getScrollElement: () => parentRef.current,
  estimateSize: () => 50,
});
```

### Memoization
```typescript
// Expensive computations are memoized
const processedData = useMemo(() => {
  return expensiveProcessing(rawData);
}, [rawData]);

// Component memoization
const MemoizedComponent = memo(ExpensiveComponent);
```

### Lazy Loading
```typescript
// Components loaded on demand
const LazyComponent = lazy(() => import('./HeavyComponent'));

// Wrapped with Suspense
<Suspense fallback={<Loading />}>
  <LazyComponent />
</Suspense>
```

This component architecture provides a scalable, maintainable foundation for the Claudia application, enabling complex interactions while maintaining clean separation of concerns and efficient data flow.
