# Claudia System Overview

## System Architecture Diagram

```mermaid
graph TB
    subgraph "User Interface Layer"
        UI[React 18 Frontend]
        Components[UI Components]
        Router[View Router]
    end
    
    subgraph "Application Layer"
        API[API Client]
        State[State Management]
        Events[Event Handlers]
    end
    
    subgraph "Tauri Bridge Layer"
        Commands[Tauri Commands]
        IPC[IPC Bridge]
        EventSystem[Event System]
    end
    
    subgraph "Backend Services Layer"
        AgentService[Agent Service]
        ClaudeService[Claude Service]
        MCPService[MCP Service]
        UsageService[Usage Service]
        CheckpointService[Checkpoint Service]
        ProcessService[Process Service]
    end
    
    subgraph "Data Layer"
        SQLite[(SQLite Database)]
        FileSystem[File System]
        ConfigFiles[Config Files]
    end
    
    subgraph "External Systems"
        ClaudeCLI[Claude Code CLI]
        MCPServers[MCP Servers]
        GitHubAPI[GitHub API]
        LocalFiles[Local Projects]
    end
    
    UI --> API
    API --> Commands
    Commands --> AgentService
    Commands --> ClaudeService
    Commands --> MCPService
    Commands --> UsageService
    Commands --> CheckpointService
    Commands --> ProcessService
    
    AgentService --> SQLite
    ClaudeService --> FileSystem
    MCPService --> ConfigFiles
    UsageService --> SQLite
    CheckpointService --> FileSystem
    ProcessService --> ClaudeCLI
    
    ClaudeService --> ClaudeCLI
    MCPService --> MCPServers
    AgentService --> GitHubAPI
    ClaudeService --> LocalFiles
    
    EventSystem --> Events
    Events --> State
    State --> UI
```

## Core Feature Flows

### 1. Agent Management Flow

```mermaid
flowchart TD
    A[User Opens CC Agents] --> B[Load Agent List]
    B --> C[Display Agent Cards]
    C --> D{User Action}
    
    D -->|Create New| E[Open Create Form]
    D -->|Edit Existing| F[Load Agent Data]
    D -->|Execute Agent| G[Start Execution]
    D -->|Import Agent| H[GitHub Browser]
    
    E --> I[Fill Agent Details]
    I --> J[Save to Database]
    J --> K[Refresh Agent List]
    
    F --> L[Edit Form]
    L --> M[Update Database]
    M --> K
    
    G --> N[Select Project]
    N --> O[Enter Task]
    O --> P[Spawn Claude Process]
    P --> Q[Stream Output]
    Q --> R[Update UI Real-time]
    
    H --> S[Browse GitHub Agents]
    S --> T[Select Agent]
    T --> U[Download & Import]
    U --> K
```

### 2. Claude Code Session Flow

```mermaid
flowchart TD
    A[User Starts Session] --> B[Select Project Path]
    B --> C[Enter Initial Prompt]
    C --> D[Execute Claude Code]
    D --> E[Stream JSONL Output]
    E --> F[Parse Messages]
    F --> G[Update Chat UI]
    
    G --> H{Tool Usage Detected?}
    H -->|Yes| I[Auto Checkpoint]
    H -->|No| J[Continue Session]
    
    I --> K[Scan File Changes]
    K --> L[Create Snapshots]
    L --> M[Save Checkpoint]
    M --> N[Update Timeline]
    N --> J
    
    J --> O{User Input?}
    O -->|New Prompt| P[Continue Session]
    O -->|Navigate Timeline| Q[Load Checkpoint]
    O -->|Fork Session| R[Create Branch]
    
    P --> D
    Q --> S[Restore File State]
    S --> T[Update Session]
    T --> G
    
    R --> U[New Session Branch]
    U --> C
```

### 3. MCP Server Management Flow

```mermaid
flowchart TD
    A[User Opens MCP Manager] --> B[Load Server List]
    B --> C[Display Server Status]
    C --> D{User Action}
    
    D -->|Add Server| E[Server Configuration]
    D -->|Test Connection| F[Connection Test]
    D -->|Import from Claude Desktop| G[Import Config]
    D -->|Remove Server| H[Confirm Removal]
    
    E --> I[Enter Server Details]
    I --> J[Validate Configuration]
    J --> K[Save Server Config]
    K --> L[Test Connection]
    L --> M[Update Server Status]
    M --> N[Refresh Server List]
    
    F --> O[Execute Test Command]
    O --> P[Check Response]
    P --> Q{Connection OK?}
    Q -->|Yes| R[Mark Server Active]
    Q -->|No| S[Mark Server Error]
    R --> N
    S --> N
    
    G --> T[Read Claude Desktop Config]
    T --> U[Parse Server Configs]
    U --> V[Import Multiple Servers]
    V --> N
    
    H --> W[Remove from Config]
    W --> N
```

## Data Storage Architecture

```mermaid
erDiagram
    AGENTS {
        int id PK
        string name
        string icon
        text system_prompt
        string default_task
        string model
        datetime created_at
        datetime updated_at
    }
    
    AGENT_RUNS {
        int id PK
        int agent_id FK
        string agent_name
        string agent_icon
        text task
        string model
        string project_path
        string session_id
        string status
        int pid
        datetime process_started_at
        datetime created_at
        datetime completed_at
    }
    
    CHECKPOINTS {
        string id PK
        string session_id
        string project_id
        int message_index
        datetime timestamp
        text description
        string parent_checkpoint_id
        int total_tokens
        string model_used
        text user_prompt
        int file_changes
        int snapshot_size
    }
    
    FILE_SNAPSHOTS {
        string checkpoint_id FK
        string file_path
        text content
        string hash
        boolean is_deleted
        int permissions
        int size
    }
    
    MCP_SERVERS {
        string name PK
        string transport
        string command
        text args
        text env
        string url
        string scope
        boolean is_active
        text status
    }
    
    APP_SETTINGS {
        string key PK
        text value
    }
    
    AGENTS ||--o{ AGENT_RUNS : "has many"
    CHECKPOINTS ||--o{ FILE_SNAPSHOTS : "contains"
```

## Process Management Architecture

```mermaid
stateDiagram-v2
    [*] --> Idle
    
    Idle --> Starting : Execute Agent/Session
    Starting --> Running : Process Spawned
    Starting --> Failed : Spawn Error
    
    Running --> Streaming : Output Available
    Streaming --> Running : Continue Processing
    Running --> Completed : Process Finished
    Running --> Failed : Process Error
    Running --> Cancelled : User Cancellation
    
    Completed --> [*]
    Failed --> [*]
    Cancelled --> [*]
    
    note right of Running
        Process Registry tracks:
        - PID
        - Start time
        - Resource usage
        - Output streams
    end note
```

## Security Model

```mermaid
flowchart TD
    A[User Request] --> B{Permission Check}
    B -->|Allowed| C[Execute Operation]
    B -->|Denied| D[Return Error]
    
    C --> E{File System Access?}
    E -->|Yes| F[Check File Permissions]
    E -->|No| G[Process Request]
    
    F --> H{Path Allowed?}
    H -->|Yes| I[Perform File Operation]
    H -->|No| J[Access Denied]
    
    G --> K{Network Access?}
    K -->|Yes| L[Check Network Policy]
    K -->|No| M[Execute Locally]
    
    L --> N{Endpoint Allowed?}
    N -->|Yes| O[Make Network Request]
    N -->|No| P[Network Denied]
    
    I --> Q[Return Result]
    M --> Q
    O --> Q
    J --> R[Return Error]
    P --> R
    D --> R
```

## Integration Points

### Claude Code CLI Integration

```mermaid
sequenceDiagram
    participant App as Claudia
    participant CLI as Claude CLI
    participant API as Claude API
    participant FS as File System
    
    App->>CLI: Execute command with args
    CLI->>FS: Read project files
    CLI->>API: Send request with context
    API-->>CLI: Stream response
    
    loop Stream Processing
        CLI-->>App: JSONL output chunk
        App->>App: Parse and display
    end
    
    CLI->>FS: Write file changes
    CLI-->>App: Process complete
    App->>App: Update UI state
```

### GitHub Integration

```mermaid
sequenceDiagram
    participant UI as Claudia UI
    participant API as GitHub API
    participant Parser as JSON Parser
    participant DB as Local Database
    
    UI->>API: Fetch repository contents
    API-->>UI: File list with metadata
    UI->>API: Download specific agent file
    API-->>Parser: Raw JSON content
    Parser->>Parser: Validate agent format
    Parser->>DB: Store agent locally
    DB-->>UI: Confirm import success
```

## Performance Characteristics

### Frontend Performance

```mermaid
graph LR
    A[Component Mount] --> B[Initial Render]
    B --> C[Data Fetch]
    C --> D[State Update]
    D --> E[Re-render]
    E --> F[Virtual DOM Diff]
    F --> G[DOM Update]
    
    H[User Interaction] --> I[Event Handler]
    I --> J[State Change]
    J --> K[Optimized Re-render]
    K --> L[Minimal DOM Updates]
```

### Backend Performance

```mermaid
graph TD
    A[Request Received] --> B[Command Validation]
    B --> C[Database Query]
    C --> D[Business Logic]
    D --> E[External API Call]
    E --> F[Response Processing]
    F --> G[Result Serialization]
    G --> H[Response Sent]
    
    I[Background Process] --> J[File System Monitor]
    J --> K[Change Detection]
    K --> L[Event Emission]
    L --> M[UI Update]
```

This system overview provides a comprehensive understanding of how Claudia's components work together to deliver a powerful Claude Code management experience while maintaining security, performance, and reliability.
