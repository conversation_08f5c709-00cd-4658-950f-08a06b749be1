use anyhow::{Context, Result};
use log::{info, warn};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;
use tauri::{command, AppHandle};

/// SuperClaude command information
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SuperClaudeCommand {
    pub name: String,
    pub description: String,
    pub category: String,
    pub flags: Vec<String>,
}

/// SuperClaude persona information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuperClaudePersona {
    pub name: String,
    pub description: String,
    pub expertise: String,
    pub best_for: String,
}

/// SuperClaude execution configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct SuperClaudeExecutionConfig {
    pub command: String,
    pub flags: Vec<String>,
    pub persona: Option<String>,
    pub mcp_servers: Vec<String>,
    pub thinking_depth: ThinkingDepth,
    pub token_optimization: bool,
}

/// Thinking depth levels for SuperClaude
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum ThinkingDepth {
    Standard,  // --think (~4K tokens)
    Deep,      // --think-hard (~10K tokens)
    Ultra,     // --ultrathink (~32K tokens)
}

impl ThinkingDepth {
    pub fn token_usage(&self) -> u32 {
        match self {
            ThinkingDepth::Standard => 4000,
            ThinkingDepth::Deep => 10000,
            ThinkingDepth::Ultra => 32000,
        }
    }

    pub fn to_flag(&self) -> String {
        match self {
            ThinkingDepth::Standard => "--think".to_string(),
            ThinkingDepth::Deep => "--think-hard".to_string(),
            ThinkingDepth::Ultra => "--ultrathink".to_string(),
        }
    }
}

/// SuperClaude workflow step
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuperClaudeWorkflowStep {
    pub name: String,
    pub command: String,
    pub flags: Vec<String>,
    pub persona: Option<String>,
}

/// SuperClaude workflow definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SuperClaudeWorkflow {
    pub name: String,
    pub description: String,
    pub steps: Vec<SuperClaudeWorkflowStep>,
}

/// Check if SuperClaude is available on the system
#[command]
pub async fn check_superclaude_availability() -> Result<bool, String> {
    info!("Checking SuperClaude availability");
    
    // First check if Claude CLI is available
    let claude_available = Command::new("which")
        .arg("claude")
        .output()
        .or_else(|_| {
            // Try Windows where command
            Command::new("where")
                .arg("claude")
                .output()
        });

    let claude_exists = match claude_available {
        Ok(output) => output.status.success(),
        Err(_) => false,
    };

    if !claude_exists {
        info!("Claude CLI not found, SuperClaude not available");
        return Ok(false);
    }

    // Check if SuperClaude configuration exists
    let home_dir = dirs::home_dir()
        .ok_or_else(|| "Could not find home directory".to_string())?;
    
    let superclaude_config = home_dir.join(".claude").join("CLAUDE.md");
    let superclaude_commands = home_dir.join(".claude").join("commands");
    
    let available = superclaude_config.exists() && superclaude_commands.exists();
    info!("SuperClaude availability: {}", available);
    
    Ok(available)
}

/// Get available SuperClaude commands
#[command]
pub async fn get_available_superclaude_commands() -> Result<Vec<SuperClaudeCommand>, String> {
    info!("Getting available SuperClaude commands");
    
    // Return predefined command list based on SuperClaude documentation
    let commands = vec![
        // Development Commands
        SuperClaudeCommand {
            name: "/build".to_string(),
            description: "Universal Project Builder. Build projects, features, and components using modern stack templates.".to_string(),
            category: "Development".to_string(),
            flags: vec!["--init".to_string(), "--feature".to_string(), "--tdd".to_string(), "--react".to_string(), "--api".to_string(), "--fullstack".to_string()],
        },
        SuperClaudeCommand {
            name: "/dev-setup".to_string(),
            description: "Development environment setup".to_string(),
            category: "Development".to_string(),
            flags: vec!["--install".to_string(), "--ci".to_string(), "--monitor".to_string(), "--docker".to_string(), "--testing".to_string()],
        },
        SuperClaudeCommand {
            name: "/test".to_string(),
            description: "Testing framework".to_string(),
            category: "Development".to_string(),
            flags: vec!["--e2e".to_string(), "--integration".to_string(), "--unit".to_string(), "--coverage".to_string(), "--performance".to_string()],
        },
        
        // Analysis & Improvement Commands
        SuperClaudeCommand {
            name: "/review".to_string(),
            description: "AI-powered code review with evidence-based recommendations".to_string(),
            category: "Analysis & Improvement".to_string(),
            flags: vec!["--files".to_string(), "--commit".to_string(), "--pr".to_string(), "--quality".to_string(), "--evidence".to_string(), "--fix".to_string()],
        },
        SuperClaudeCommand {
            name: "/analyze".to_string(),
            description: "Code and system analysis".to_string(),
            category: "Analysis & Improvement".to_string(),
            flags: vec!["--code".to_string(), "--architecture".to_string(), "--profile".to_string(), "--deps".to_string(), "--surface".to_string(), "--deep".to_string()],
        },
        SuperClaudeCommand {
            name: "/troubleshoot".to_string(),
            description: "Debugging and issue resolution".to_string(),
            category: "Analysis & Improvement".to_string(),
            flags: vec!["--investigate".to_string(), "--five-whys".to_string(), "--prod".to_string(), "--perf".to_string(), "--fix".to_string()],
        },
        SuperClaudeCommand {
            name: "/improve".to_string(),
            description: "Enhancement and optimization".to_string(),
            category: "Analysis & Improvement".to_string(),
            flags: vec!["--quality".to_string(), "--performance".to_string(), "--accessibility".to_string(), "--iterate".to_string(), "--refactor".to_string()],
        },
        SuperClaudeCommand {
            name: "/explain".to_string(),
            description: "Documentation and explanations".to_string(),
            category: "Analysis & Improvement".to_string(),
            flags: vec!["--depth".to_string(), "--visual".to_string(), "--examples".to_string(), "--api".to_string(), "--tutorial".to_string()],
        },
        
        // Operations Commands
        SuperClaudeCommand {
            name: "/deploy".to_string(),
            description: "Application deployment".to_string(),
            category: "Operations".to_string(),
            flags: vec!["--env".to_string(), "--canary".to_string(), "--blue-green".to_string(), "--rolling".to_string(), "--monitor".to_string()],
        },
        SuperClaudeCommand {
            name: "/migrate".to_string(),
            description: "Database and code migrations".to_string(),
            category: "Operations".to_string(),
            flags: vec!["--database".to_string(), "--code".to_string(), "--backup".to_string(), "--rollback".to_string(), "--validate".to_string()],
        },
        SuperClaudeCommand {
            name: "/scan".to_string(),
            description: "Security and validation".to_string(),
            category: "Operations".to_string(),
            flags: vec!["--security".to_string(), "--owasp".to_string(), "--deps".to_string(), "--compliance".to_string(), "--quality".to_string()],
        },
        SuperClaudeCommand {
            name: "/estimate".to_string(),
            description: "Project estimation".to_string(),
            category: "Operations".to_string(),
            flags: vec!["--detailed".to_string(), "--rough".to_string(), "--worst-case".to_string(), "--agile".to_string(), "--complexity".to_string()],
        },
        SuperClaudeCommand {
            name: "/cleanup".to_string(),
            description: "Project maintenance".to_string(),
            category: "Operations".to_string(),
            flags: vec!["--code".to_string(), "--files".to_string(), "--deps".to_string(), "--git".to_string(), "--all".to_string()],
        },
        SuperClaudeCommand {
            name: "/git".to_string(),
            description: "Git workflow management".to_string(),
            category: "Operations".to_string(),
            flags: vec!["--status".to_string(), "--commit".to_string(), "--branch".to_string(), "--sync".to_string(), "--checkpoint".to_string()],
        },
        
        // Design & Workflow Commands
        SuperClaudeCommand {
            name: "/design".to_string(),
            description: "System architecture".to_string(),
            category: "Design & Workflow".to_string(),
            flags: vec!["--api".to_string(), "--ddd".to_string(), "--microservices".to_string(), "--event-driven".to_string(), "--openapi".to_string()],
        },
        SuperClaudeCommand {
            name: "/spawn".to_string(),
            description: "Parallel task execution".to_string(),
            category: "Design & Workflow".to_string(),
            flags: vec!["--task".to_string(), "--parallel".to_string(), "--specialized".to_string(), "--collaborative".to_string(), "--sync".to_string()],
        },
        SuperClaudeCommand {
            name: "/document".to_string(),
            description: "Documentation creation".to_string(),
            category: "Design & Workflow".to_string(),
            flags: vec!["--user".to_string(), "--technical".to_string(), "--markdown".to_string(), "--interactive".to_string(), "--multilingual".to_string()],
        },
        SuperClaudeCommand {
            name: "/load".to_string(),
            description: "Project context loading".to_string(),
            category: "Design & Workflow".to_string(),
            flags: vec!["--depth".to_string(), "--context".to_string(), "--patterns".to_string(), "--relationships".to_string(), "--structure".to_string()],
        },
        SuperClaudeCommand {
            name: "/task".to_string(),
            description: "Task management".to_string(),
            category: "Design & Workflow".to_string(),
            flags: vec!["create".to_string(), "status".to_string(), "resume".to_string(), "update".to_string(), "complete".to_string()],
        },
    ];
    
    Ok(commands)
}

/// Get available SuperClaude personas
#[command]
pub async fn get_available_superclaude_personas() -> Result<Vec<SuperClaudePersona>, String> {
    info!("Getting available SuperClaude personas");
    
    let personas = vec![
        SuperClaudePersona {
            name: "architect".to_string(),
            description: "Systems thinking, scalability, patterns".to_string(),
            expertise: "Architecture decisions, system design".to_string(),
            best_for: "Complex system design and architectural decisions".to_string(),
        },
        SuperClaudePersona {
            name: "frontend".to_string(),
            description: "UI/UX obsessed, accessibility-first".to_string(),
            expertise: "User interfaces, component design".to_string(),
            best_for: "Frontend development and user experience".to_string(),
        },
        SuperClaudePersona {
            name: "backend".to_string(),
            description: "APIs, databases, reliability".to_string(),
            expertise: "Server architecture, data modeling".to_string(),
            best_for: "Backend systems and API development".to_string(),
        },
        SuperClaudePersona {
            name: "analyzer".to_string(),
            description: "Root cause analysis, evidence-based".to_string(),
            expertise: "Complex debugging, investigations".to_string(),
            best_for: "Problem analysis and troubleshooting".to_string(),
        },
        SuperClaudePersona {
            name: "security".to_string(),
            description: "Threat modeling, zero-trust, OWASP".to_string(),
            expertise: "Security audits, vulnerability assessment".to_string(),
            best_for: "Security analysis and threat assessment".to_string(),
        },
        SuperClaudePersona {
            name: "mentor".to_string(),
            description: "Teaching, guided learning, clarity".to_string(),
            expertise: "Documentation, knowledge transfer".to_string(),
            best_for: "Educational content and mentoring".to_string(),
        },
        SuperClaudePersona {
            name: "refactorer".to_string(),
            description: "Code quality, maintainability".to_string(),
            expertise: "Code cleanup, technical debt".to_string(),
            best_for: "Code improvement and refactoring".to_string(),
        },
        SuperClaudePersona {
            name: "performance".to_string(),
            description: "Optimization, profiling, efficiency".to_string(),
            expertise: "Performance tuning, bottlenecks".to_string(),
            best_for: "Performance optimization and analysis".to_string(),
        },
        SuperClaudePersona {
            name: "qa".to_string(),
            description: "Testing, edge cases, validation".to_string(),
            expertise: "Quality assurance, test coverage".to_string(),
            best_for: "Quality assurance and testing strategies".to_string(),
        },
    ];
    
    Ok(personas)
}

/// Execute Claude Code with SuperClaude enhancement
#[command]
pub async fn execute_claude_code_with_superclaude(
    app: AppHandle,
    project_path: String,
    prompt: String,
    config: SuperClaudeExecutionConfig,
) -> Result<(), String> {
    info!("Executing Claude Code with SuperClaude enhancement");
    
    // Check if SuperClaude is available
    let superclaude_available = check_superclaude_availability().await?;
    
    if !superclaude_available {
        return Err("SuperClaude is not available. Please install SuperClaude framework.".to_string());
    }
    
    // Validate the command and configuration
    validate_superclaude_command(&config.command).await
        .map_err(|e| format!("Invalid SuperClaude command: {}", e))?;
    
    if let Some(ref persona) = config.persona {
        validate_superclaude_persona(persona).await
            .map_err(|e| format!("Invalid persona: {}", e))?;
    }
    
    // Build the command arguments
    let cmd_args = build_superclaude_command_args(
        &project_path,
        &prompt,
        &config.command,
        config.flags,
        None, // System prompt handled by SuperClaude personas
    );
    
    // Execute using the router (SuperClaude works through claude-code-router)
    crate::commands::router::execute_claude_code_with_router(
        app,
        project_path,
        format!("{} {}", config.command, prompt),
        "claude-3.5-sonnet".to_string(), // Default model for SuperClaude
        None,
    ).await
}

// Internal helper functions

pub async fn validate_superclaude_command(command: &str) -> Result<()> {
    if !command.starts_with('/') {
        return Err(anyhow::anyhow!("SuperClaude commands must start with '/'"));
    }
    
    let valid_commands = [
        "/build", "/dev-setup", "/test", "/review", "/analyze", "/troubleshoot",
        "/improve", "/explain", "/deploy", "/migrate", "/scan", "/estimate",
        "/cleanup", "/git", "/design", "/spawn", "/document", "/load", "/task"
    ];
    
    if !valid_commands.contains(&command) {
        return Err(anyhow::anyhow!("Unknown SuperClaude command: {}", command));
    }
    
    Ok(())
}

pub async fn validate_superclaude_persona(persona: &str) -> Result<()> {
    let valid_personas = [
        "architect", "frontend", "backend", "analyzer", "security",
        "mentor", "refactorer", "performance", "qa"
    ];
    
    if persona.is_empty() || !valid_personas.contains(&persona) {
        return Err(anyhow::anyhow!("Invalid SuperClaude persona: {}", persona));
    }
    
    Ok(())
}

pub async fn validate_superclaude_flag(flag: &str) -> Result<()> {
    if !flag.starts_with("--") {
        return Err(anyhow::anyhow!("SuperClaude flags must start with '--'"));
    }
    
    // Validate against known SuperClaude flags
    let valid_flags = [
        "--think", "--think-hard", "--ultrathink",
        "--seq", "--magic", "--c7", "--pup", "--all-mcp", "--no-mcp",
        "--no-c7", "--no-seq", "--no-magic", "--no-pup",
        "--validate", "--security", "--coverage", "--strict",
        "--plan", "--dry-run", "--watch", "--interactive", "--force",
        "--uc", "--ultracompressed",
        "--introspect",
        // Command-specific flags
        "--init", "--feature", "--tdd", "--react", "--api", "--fullstack",
        "--files", "--commit", "--pr", "--quality", "--evidence", "--fix",
        "--code", "--architecture", "--profile", "--deps", "--surface", "--deep",
        "--investigate", "--five-whys", "--prod", "--perf", "--hotfix",
        "--iterate", "--threshold", "--refactor", "--modernize",
        "--depth", "--visual", "--examples", "--tutorial", "--reference",
        "--env", "--canary", "--blue-green", "--rolling", "--monitor",
        "--database", "--backup", "--rollback",
        "--owasp", "--secrets", "--compliance", "--automated",
        "--detailed", "--rough", "--worst-case", "--agile", "--complexity",
        "--all", "--aggressive", "--conservative",
        "--status", "--branch", "--sync", "--checkpoint", "--merge",
        "--ddd", "--microservices", "--event-driven", "--openapi", "--graphql",
        "--task", "--parallel", "--specialized", "--collaborative",
        "--user", "--technical", "--markdown", "--multilingual", "--maintain",
        "--context", "--patterns", "--relationships", "--structure", "--health"
    ];
    
    if !valid_flags.contains(&flag) {
        return Err(anyhow::anyhow!("Unknown SuperClaude flag: {}", flag));
    }
    
    Ok(())
}

pub async fn validate_superclaude_workflow(workflow: &SuperClaudeWorkflow) -> Result<()> {
    if workflow.steps.is_empty() {
        return Err(anyhow::anyhow!("Workflow must have at least one step"));
    }
    
    for step in &workflow.steps {
        validate_superclaude_command(&step.command).await
            .context(format!("Invalid command in step '{}'", step.name))?;
        
        if let Some(ref persona) = step.persona {
            validate_superclaude_persona(persona).await
                .context(format!("Invalid persona in step '{}'", step.name))?;
        }
        
        for flag in &step.flags {
            validate_superclaude_flag(flag).await
                .context(format!("Invalid flag '{}' in step '{}'", flag, step.name))?;
        }
    }
    
    Ok(())
}

pub fn build_superclaude_command_args(
    _project_path: &str,
    prompt: &str,
    command: &str,
    flags: Vec<String>,
    system_prompt: Option<&str>,
) -> Vec<String> {
    let mut args = vec![
        command.to_string(),
        "-p".to_string(),
        prompt.to_string(),
    ];
    
    // Add flags
    for flag in flags {
        args.push(flag);
    }
    
    // Add system prompt if provided
    if let Some(sys_prompt) = system_prompt {
        args.push("--system-prompt".to_string());
        args.push(sys_prompt.to_string());
    }
    
    // Add standard SuperClaude flags
    args.push("--output-format".to_string());
    args.push("stream-json".to_string());
    args.push("--verbose".to_string());
    
    args
}

pub fn build_mcp_server_flags(mcp_servers: &[String]) -> Vec<String> {
    let mut flags = Vec::new();
    
    for server in mcp_servers {
        match server.as_str() {
            "seq" => flags.push("--seq".to_string()),
            "magic" => flags.push("--magic".to_string()),
            "c7" => flags.push("--c7".to_string()),
            "pup" => flags.push("--pup".to_string()),
            _ => {
                // Custom MCP server
                flags.push(format!("--{}", server));
            }
        }
    }
    
    flags
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;
    use std::fs;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_superclaude_availability_check() {
        // Test when SuperClaude is not installed
        let result = check_superclaude_availability().await;
        // This might fail if SuperClaude is actually installed, but that's expected
        
        // The function should return a boolean without panicking
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_superclaude_command_building() {
        let project_path = "/test/project";
        let prompt = "Test prompt";
        let command = "/analyze";
        let flags = vec!["--code".to_string(), "--persona-architect".to_string()];

        let cmd_args = build_superclaude_command_args(project_path, prompt, command, flags, None);
        
        assert!(cmd_args.contains(&command.to_string()));
        assert!(cmd_args.contains(&"-p".to_string()));
        assert!(cmd_args.contains(&prompt.to_string()));
        assert!(cmd_args.contains(&"--code".to_string()));
        assert!(cmd_args.contains(&"--persona-architect".to_string()));
    }

    #[tokio::test]
    async fn test_superclaude_command_validation() {
        // Valid commands
        assert!(validate_superclaude_command("/analyze").await.is_ok());
        assert!(validate_superclaude_command("/build").await.is_ok());
        assert!(validate_superclaude_command("/review").await.is_ok());
        assert!(validate_superclaude_command("/troubleshoot").await.is_ok());

        // Invalid commands
        assert!(validate_superclaude_command("analyze").await.is_err()); // Missing slash
        assert!(validate_superclaude_command("/invalid").await.is_err()); // Unknown command
        assert!(validate_superclaude_command("").await.is_err()); // Empty command
    }

    #[tokio::test]
    async fn test_superclaude_persona_validation() {
        // Valid personas
        assert!(validate_superclaude_persona("architect").await.is_ok());
        assert!(validate_superclaude_persona("frontend").await.is_ok());
        assert!(validate_superclaude_persona("security").await.is_ok());
        assert!(validate_superclaude_persona("performance").await.is_ok());

        // Invalid personas
        assert!(validate_superclaude_persona("invalid").await.is_err());
        assert!(validate_superclaude_persona("").await.is_err());
    }

    #[tokio::test]
    async fn test_get_available_superclaude_commands() {
        let commands = get_available_superclaude_commands().await;
        assert!(commands.is_ok());
        
        let command_list = commands.unwrap();
        assert!(command_list.len() > 0);
        
        // Check for expected command categories
        let has_analysis = command_list.iter().any(|cmd| cmd.category == "Analysis & Improvement");
        let has_development = command_list.iter().any(|cmd| cmd.category == "Development");
        let has_operations = command_list.iter().any(|cmd| cmd.category == "Operations");
        
        assert!(has_analysis);
        assert!(has_development);
        assert!(has_operations);
    }

    #[tokio::test]
    async fn test_get_available_superclaude_personas() {
        let personas = get_available_superclaude_personas().await;
        assert!(personas.is_ok());
        
        let persona_list = personas.unwrap();
        assert!(persona_list.len() > 0);
        
        // Check for expected personas
        let has_architect = persona_list.iter().any(|p| p.name == "architect");
        let has_frontend = persona_list.iter().any(|p| p.name == "frontend");
        let has_security = persona_list.iter().any(|p| p.name == "security");
        
        assert!(has_architect);
        assert!(has_frontend);
        assert!(has_security);
    }

    #[tokio::test]
    async fn test_superclaude_flag_validation() {
        // Valid flags
        assert!(validate_superclaude_flag("--think").await.is_ok());
        assert!(validate_superclaude_flag("--seq").await.is_ok());
        assert!(validate_superclaude_flag("--magic").await.is_ok());
        assert!(validate_superclaude_flag("--validate").await.is_ok());

        // Invalid flags
        assert!(validate_superclaude_flag("think").await.is_err()); // Missing --
        assert!(validate_superclaude_flag("--invalid").await.is_err()); // Unknown flag
        assert!(validate_superclaude_flag("").await.is_err()); // Empty flag
    }

    #[tokio::test]
    async fn test_superclaude_execution_config() {
        let config = SuperClaudeExecutionConfig {
            command: "/analyze".to_string(),
            flags: vec!["--code".to_string(), "--think".to_string()],
            persona: Some("architect".to_string()),
            mcp_servers: vec!["seq".to_string(), "magic".to_string()],
            thinking_depth: ThinkingDepth::Standard,
            token_optimization: false,
        };

        // Test serialization/deserialization
        let json = serde_json::to_string(&config).unwrap();
        let deserialized: SuperClaudeExecutionConfig = serde_json::from_str(&json).unwrap();
        
        assert_eq!(config.command, deserialized.command);
        assert_eq!(config.flags, deserialized.flags);
        assert_eq!(config.persona, deserialized.persona);
        assert_eq!(config.mcp_servers, deserialized.mcp_servers);
    }

    #[tokio::test]
    async fn test_thinking_depth_token_usage() {
        assert_eq!(ThinkingDepth::Standard.token_usage(), 4000);
        assert_eq!(ThinkingDepth::Deep.token_usage(), 10000);
        assert_eq!(ThinkingDepth::Ultra.token_usage(), 32000);
    }

    #[tokio::test]
    async fn test_superclaude_command_with_system_prompt() {
        let project_path = "/test/project";
        let prompt = "Test prompt";
        let command = "/build";
        let flags = vec!["--react".to_string(), "--tdd".to_string()];
        let system_prompt = Some("You are a senior React developer");

        let cmd_args = build_superclaude_command_args(project_path, prompt, command, flags, system_prompt);
        
        assert!(cmd_args.contains(&"--system-prompt".to_string()));
        assert!(cmd_args.contains(&"You are a senior React developer".to_string()));
    }

    #[tokio::test]
    async fn test_superclaude_workflow_validation() {
        // Test valid workflow
        let workflow = SuperClaudeWorkflow {
            name: "Full-Stack Development".to_string(),
            description: "Complete development workflow".to_string(),
            steps: vec![
                SuperClaudeWorkflowStep {
                    name: "Design".to_string(),
                    command: "/design".to_string(),
                    flags: vec!["--api".to_string(), "--ddd".to_string()],
                    persona: Some("architect".to_string()),
                },
                SuperClaudeWorkflowStep {
                    name: "Build".to_string(),
                    command: "/build".to_string(),
                    flags: vec!["--react".to_string(), "--tdd".to_string()],
                    persona: Some("frontend".to_string()),
                },
            ],
        };

        let result = validate_superclaude_workflow(&workflow).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_superclaude_mcp_server_integration() {
        let mcp_servers = vec!["seq".to_string(), "magic".to_string(), "c7".to_string()];
        let flags = build_mcp_server_flags(&mcp_servers);
        
        assert!(flags.contains(&"--seq".to_string()));
        assert!(flags.contains(&"--magic".to_string()));
        assert!(flags.contains(&"--c7".to_string()));
    }
}
