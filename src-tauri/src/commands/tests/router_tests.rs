#[cfg(test)]
mod router_tests {
    use super::super::router::*;
    use serde_json::json;
    use std::fs;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_router_config_creation() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("config.json");
        
        let config = RouterConfig {
            openai_api_key: Some("sk-test".to_string()),
            openai_base_url: Some("https://api.openai.com".to_string()),
            openai_model: Some("gpt-4".to_string()),
            providers: vec![
                Provider {
                    name: "openai".to_string(),
                    api_base_url: "https://api.openai.com".to_string(),
                    api_key: "sk-test".to_string(),
                    models: vec!["gpt-4".to_string(), "gpt-3.5-turbo".to_string()],
                }
            ],
            router: Some(RouterStrategy {
                background: Some("openai,gpt-3.5-turbo".to_string()),
                think: Some("openai,gpt-4".to_string()),
                long_context: Some("openai,gpt-4".to_string()),
            }),
            use_plugins: Some(vec!["toolcall-improvement".to_string()]),
        };

        let result = save_router_config(&config_path, &config).await;
        assert!(result.is_ok());
        assert!(config_path.exists());
    }

    #[tokio::test]
    async fn test_router_config_loading() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("config.json");
        
        let config_json = json!({
            "OPENAI_API_KEY": "sk-test",
            "OPENAI_BASE_URL": "https://api.openai.com",
            "OPENAI_MODEL": "gpt-4",
            "Providers": [
                {
                    "name": "openai",
                    "api_base_url": "https://api.openai.com",
                    "api_key": "sk-test",
                    "models": ["gpt-4", "gpt-3.5-turbo"]
                }
            ],
            "Router": {
                "background": "openai,gpt-3.5-turbo",
                "think": "openai,gpt-4",
                "longContext": "openai,gpt-4"
            },
            "usePlugins": ["toolcall-improvement"]
        });

        fs::write(&config_path, config_json.to_string()).unwrap();
        
        let result = load_router_config(&config_path).await;
        assert!(result.is_ok());
        
        let config = result.unwrap();
        assert_eq!(config.openai_api_key, Some("sk-test".to_string()));
        assert_eq!(config.providers.len(), 1);
        assert_eq!(config.providers[0].name, "openai");
    }

    #[tokio::test]
    async fn test_router_availability_check() {
        // Test when router is not installed
        let result = check_router_availability().await;
        // This might fail if router is actually installed, but that's expected
        
        // Test when router is installed (mock scenario)
        // We'll implement this when we have the actual command execution
    }

    #[tokio::test]
    async fn test_get_available_models() {
        let config = RouterConfig {
            openai_api_key: Some("sk-test".to_string()),
            openai_base_url: Some("https://api.openai.com".to_string()),
            openai_model: Some("gpt-4".to_string()),
            providers: vec![
                Provider {
                    name: "openai".to_string(),
                    api_base_url: "https://api.openai.com".to_string(),
                    api_key: "sk-test".to_string(),
                    models: vec!["gpt-4".to_string(), "gpt-3.5-turbo".to_string()],
                },
                Provider {
                    name: "anthropic".to_string(),
                    api_base_url: "https://api.anthropic.com".to_string(),
                    api_key: "sk-ant-test".to_string(),
                    models: vec!["claude-3.5-sonnet".to_string()],
                }
            ],
            router: None,
            use_plugins: None,
        };

        let models = get_available_models_from_config(&config);
        assert_eq!(models.len(), 3);
        assert!(models.contains(&"openai,gpt-4".to_string()));
        assert!(models.contains(&"openai,gpt-3.5-turbo".to_string()));
        assert!(models.contains(&"anthropic,claude-3.5-sonnet".to_string()));
    }

    #[tokio::test]
    async fn test_validate_router_model() {
        let config = RouterConfig {
            openai_api_key: Some("sk-test".to_string()),
            openai_base_url: None,
            openai_model: None,
            providers: vec![
                Provider {
                    name: "openai".to_string(),
                    api_base_url: "https://api.openai.com".to_string(),
                    api_key: "sk-test".to_string(),
                    models: vec!["gpt-4".to_string()],
                }
            ],
            router: None,
            use_plugins: None,
        };

        // Valid model
        let result = validate_router_model(&config, "openai,gpt-4").await;
        assert!(result.is_ok());

        // Invalid model
        let result = validate_router_model(&config, "openai,gpt-5").await;
        assert!(result.is_err());

        // Invalid provider
        let result = validate_router_model(&config, "invalid,gpt-4").await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_router_command_building() {
        let project_path = "/test/project";
        let prompt = "Test prompt";
        let model = "openai,gpt-4";

        let cmd_args = build_router_command_args(project_path, prompt, model, None);
        
        assert!(cmd_args.contains(&"code".to_string()));
        assert!(cmd_args.contains(&"-p".to_string()));
        assert!(cmd_args.contains(&prompt.to_string()));
        assert!(cmd_args.contains(&"--model".to_string()));
        assert!(cmd_args.contains(&model.to_string()));
    }
}

#[cfg(test)]
mod integration_tests {
    use super::super::router::*;
    use std::process::Command;

    #[tokio::test]
    async fn test_router_installation_detection() {
        // Test if we can detect router installation
        let output = Command::new("which")
            .arg("ccr")
            .output();
            
        match output {
            Ok(output) => {
                if output.status.success() {
                    // Router is installed, test basic functionality
                    let result = check_router_availability().await;
                    assert!(result.is_ok());
                } else {
                    // Router not installed, should handle gracefully
                    let result = check_router_availability().await;
                    assert!(result.is_err());
                }
            }
            Err(_) => {
                // Command failed, router definitely not available
                let result = check_router_availability().await;
                assert!(result.is_err());
            }
        }
    }

    #[tokio::test]
    async fn test_fallback_to_claude_cli() {
        // Test that when router is not available, we fall back to Claude CLI
        let project_path = "/test/project";
        let prompt = "Test prompt";
        let model = "claude-3.5-sonnet"; // Standard Claude model

        // This should work even without router
        let cmd_args = build_fallback_command_args(project_path, prompt, &model);
        
        assert!(cmd_args.contains(&"-p".to_string()));
        assert!(cmd_args.contains(&prompt.to_string()));
        assert!(cmd_args.contains(&"--model".to_string()));
        assert!(cmd_args.contains(&model.to_string()));
    }
}
