#[cfg(test)]
mod superclaude_tests {
    use super::super::superclaude::*;
    use serde_json::json;
    use std::fs;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_superclaude_availability_check() {
        // Test when SuperClaude is not installed
        let result = check_superclaude_availability().await;
        // This might fail if SuperClaude is actually installed, but that's expected
        
        // The function should return a boolean without panicking
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_superclaude_command_building() {
        let project_path = "/test/project";
        let prompt = "Test prompt";
        let command = "/analyze";
        let flags = vec!["--code".to_string(), "--persona-architect".to_string()];

        let cmd_args = build_superclaude_command_args(project_path, prompt, command, flags, None);
        
        assert!(cmd_args.contains(&command.to_string()));
        assert!(cmd_args.contains(&"-p".to_string()));
        assert!(cmd_args.contains(&prompt.to_string()));
        assert!(cmd_args.contains(&"--code".to_string()));
        assert!(cmd_args.contains(&"--persona-architect".to_string()));
    }

    #[tokio::test]
    async fn test_superclaude_command_validation() {
        // Valid commands
        assert!(validate_superclaude_command("/analyze").await.is_ok());
        assert!(validate_superclaude_command("/build").await.is_ok());
        assert!(validate_superclaude_command("/review").await.is_ok());
        assert!(validate_superclaude_command("/troubleshoot").await.is_ok());

        // Invalid commands
        assert!(validate_superclaude_command("analyze").await.is_err()); // Missing slash
        assert!(validate_superclaude_command("/invalid").await.is_err()); // Unknown command
        assert!(validate_superclaude_command("").await.is_err()); // Empty command
    }

    #[tokio::test]
    async fn test_superclaude_persona_validation() {
        // Valid personas
        assert!(validate_superclaude_persona("architect").await.is_ok());
        assert!(validate_superclaude_persona("frontend").await.is_ok());
        assert!(validate_superclaude_persona("security").await.is_ok());
        assert!(validate_superclaude_persona("performance").await.is_ok());

        // Invalid personas
        assert!(validate_superclaude_persona("invalid").await.is_err());
        assert!(validate_superclaude_persona("").await.is_err());
    }

    #[tokio::test]
    async fn test_get_available_superclaude_commands() {
        let commands = get_available_superclaude_commands().await;
        assert!(commands.is_ok());
        
        let command_list = commands.unwrap();
        assert!(command_list.len() > 0);
        
        // Check for expected command categories
        let has_analysis = command_list.iter().any(|cmd| cmd.category == "Analysis & Improvement");
        let has_development = command_list.iter().any(|cmd| cmd.category == "Development");
        let has_operations = command_list.iter().any(|cmd| cmd.category == "Operations");
        
        assert!(has_analysis);
        assert!(has_development);
        assert!(has_operations);
    }

    #[tokio::test]
    async fn test_get_available_superclaude_personas() {
        let personas = get_available_superclaude_personas().await;
        assert!(personas.is_ok());
        
        let persona_list = personas.unwrap();
        assert!(persona_list.len() > 0);
        
        // Check for expected personas
        let has_architect = persona_list.iter().any(|p| p.name == "architect");
        let has_frontend = persona_list.iter().any(|p| p.name == "frontend");
        let has_security = persona_list.iter().any(|p| p.name == "security");
        
        assert!(has_architect);
        assert!(has_frontend);
        assert!(has_security);
    }

    #[tokio::test]
    async fn test_superclaude_flag_validation() {
        // Valid flags
        assert!(validate_superclaude_flag("--think").await.is_ok());
        assert!(validate_superclaude_flag("--seq").await.is_ok());
        assert!(validate_superclaude_flag("--magic").await.is_ok());
        assert!(validate_superclaude_flag("--validate").await.is_ok());

        // Invalid flags
        assert!(validate_superclaude_flag("think").await.is_err()); // Missing --
        assert!(validate_superclaude_flag("--invalid").await.is_err()); // Unknown flag
        assert!(validate_superclaude_flag("").await.is_err()); // Empty flag
    }

    #[tokio::test]
    async fn test_superclaude_execution_config() {
        let config = SuperClaudeExecutionConfig {
            command: "/analyze".to_string(),
            flags: vec!["--code".to_string(), "--think".to_string()],
            persona: Some("architect".to_string()),
            mcp_servers: vec!["seq".to_string(), "magic".to_string()],
            thinking_depth: ThinkingDepth::Standard,
            token_optimization: false,
        };

        // Test serialization/deserialization
        let json = serde_json::to_string(&config).unwrap();
        let deserialized: SuperClaudeExecutionConfig = serde_json::from_str(&json).unwrap();
        
        assert_eq!(config.command, deserialized.command);
        assert_eq!(config.flags, deserialized.flags);
        assert_eq!(config.persona, deserialized.persona);
        assert_eq!(config.mcp_servers, deserialized.mcp_servers);
    }

    #[tokio::test]
    async fn test_thinking_depth_token_usage() {
        assert_eq!(ThinkingDepth::Standard.token_usage(), 4000);
        assert_eq!(ThinkingDepth::Deep.token_usage(), 10000);
        assert_eq!(ThinkingDepth::Ultra.token_usage(), 32000);
    }

    #[tokio::test]
    async fn test_superclaude_command_with_system_prompt() {
        let project_path = "/test/project";
        let prompt = "Test prompt";
        let command = "/build";
        let flags = vec!["--react".to_string(), "--tdd".to_string()];
        let system_prompt = Some("You are a senior React developer");

        let cmd_args = build_superclaude_command_args(project_path, prompt, command, flags, system_prompt);
        
        assert!(cmd_args.contains(&"--system-prompt".to_string()));
        assert!(cmd_args.contains(&"You are a senior React developer".to_string()));
    }

    #[tokio::test]
    async fn test_superclaude_workflow_validation() {
        // Test valid workflow
        let workflow = SuperClaudeWorkflow {
            name: "Full-Stack Development".to_string(),
            description: "Complete development workflow".to_string(),
            steps: vec![
                SuperClaudeWorkflowStep {
                    name: "Design".to_string(),
                    command: "/design".to_string(),
                    flags: vec!["--api".to_string(), "--ddd".to_string()],
                    persona: Some("architect".to_string()),
                },
                SuperClaudeWorkflowStep {
                    name: "Build".to_string(),
                    command: "/build".to_string(),
                    flags: vec!["--react".to_string(), "--tdd".to_string()],
                    persona: Some("frontend".to_string()),
                },
            ],
        };

        let result = validate_superclaude_workflow(&workflow).await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_superclaude_mcp_server_integration() {
        let mcp_servers = vec!["seq".to_string(), "magic".to_string(), "c7".to_string()];
        let flags = build_mcp_server_flags(&mcp_servers);
        
        assert!(flags.contains(&"--seq".to_string()));
        assert!(flags.contains(&"--magic".to_string()));
        assert!(flags.contains(&"--c7".to_string()));
    }
}

#[cfg(test)]
mod integration_tests {
    use super::super::superclaude::*;
    use std::process::Command;

    #[tokio::test]
    async fn test_superclaude_installation_detection() {
        // Test if we can detect SuperClaude installation
        let output = Command::new("which")
            .arg("claude")
            .output();
            
        match output {
            Ok(output) => {
                if output.status.success() {
                    // Claude CLI is installed, check for SuperClaude
                    let result = check_superclaude_availability().await;
                    // Should not error even if SuperClaude is not installed
                    assert!(result.is_ok());
                } else {
                    // Claude CLI not installed, SuperClaude definitely not available
                    let result = check_superclaude_availability().await;
                    assert!(result.is_ok());
                    assert!(!result.unwrap());
                }
            }
            Err(_) => {
                // Command failed, Claude CLI definitely not available
                let result = check_superclaude_availability().await;
                assert!(result.is_ok());
                assert!(!result.unwrap());
            }
        }
    }

    #[tokio::test]
    async fn test_superclaude_command_execution_dry_run() {
        // Test that we can build command arguments without executing
        let project_path = "/test/project";
        let prompt = "Analyze this codebase";
        let command = "/analyze";
        let flags = vec!["--code".to_string(), "--dry-run".to_string()];

        let cmd_args = build_superclaude_command_args(project_path, prompt, command, flags, None);
        
        // Should contain dry-run flag for safe testing
        assert!(cmd_args.contains(&"--dry-run".to_string()));
        assert!(cmd_args.contains(&command.to_string()));
        assert!(cmd_args.contains(&prompt.to_string()));
    }
}
