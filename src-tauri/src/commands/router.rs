use anyhow::{Context, Result};
use log::{info, warn};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use std::process::Command;
use tauri::{command, AppHandle};

/// Router configuration structure matching claude-code-router format
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouterConfig {
    #[serde(rename = "OPENAI_API_KEY")]
    pub openai_api_key: Option<String>,
    #[serde(rename = "OPENAI_BASE_URL")]
    pub openai_base_url: Option<String>,
    #[serde(rename = "OPENAI_MODEL")]
    pub openai_model: Option<String>,
    #[serde(rename = "Providers")]
    pub providers: Vec<Provider>,
    #[serde(rename = "Router")]
    pub router: Option<RouterStrategy>,
    #[serde(rename = "usePlugins")]
    pub use_plugins: Option<Vec<String>>,
}

/// Provider configuration for different AI services
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Provider {
    pub name: String,
    pub api_base_url: String,
    pub api_key: String,
    pub models: Vec<String>,
}

/// Router strategy for different use cases
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RouterStrategy {
    pub background: Option<String>,
    pub think: Option<String>,
    #[serde(rename = "longContext")]
    pub long_context: Option<String>,
}

/// Model information for UI display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelInfo {
    pub id: String,
    pub name: String,
    pub provider: String,
    pub description: Option<String>,
}

/// Get the router configuration directory path
fn get_router_config_dir() -> Result<PathBuf> {
    let home_dir = dirs::home_dir()
        .ok_or_else(|| anyhow::anyhow!("Could not find home directory"))?;
    Ok(home_dir.join(".claude-code-router"))
}

/// Get the router configuration file path
fn get_router_config_path() -> Result<PathBuf> {
    Ok(get_router_config_dir()?.join("config.json"))
}

/// Check if claude-code-router is available on the system
#[command]
pub async fn check_router_availability() -> Result<bool, String> {
    info!("Checking claude-code-router availability");
    
    let output = Command::new("which")
        .arg("ccr")
        .output()
        .or_else(|_| {
            // Try Windows where command
            Command::new("where")
                .arg("ccr")
                .output()
        });

    match output {
        Ok(output) => {
            let available = output.status.success();
            info!("Router availability: {}", available);
            Ok(available)
        }
        Err(e) => {
            warn!("Failed to check router availability: {}", e);
            Ok(false)
        }
    }
}

/// Load router configuration from file
#[command]
pub async fn get_router_config() -> Result<RouterConfig, String> {
    let config_path = get_router_config_path()
        .map_err(|e| format!("Failed to get config path: {}", e))?;
    
    load_router_config(&config_path).await
        .map_err(|e| format!("Failed to load router config: {}", e))
}

/// Save router configuration to file
#[command]
pub async fn save_router_config_command(config: RouterConfig) -> Result<(), String> {
    let config_path = get_router_config_path()
        .map_err(|e| format!("Failed to get config path: {}", e))?;
    
    // Ensure directory exists
    if let Some(parent) = config_path.parent() {
        fs::create_dir_all(parent)
            .map_err(|e| format!("Failed to create config directory: {}", e))?;
    }
    
    save_router_config(&config_path, &config).await
        .map_err(|e| format!("Failed to save router config: {}", e))
}

/// Get available models from router configuration
#[command]
pub async fn get_available_router_models() -> Result<Vec<String>, String> {
    let config = get_router_config().await?;
    Ok(get_available_models_from_config(&config))
}

/// Validate if a router model is available and properly configured
#[command]
pub async fn validate_router_model_command(model: String) -> Result<bool, String> {
    let config = get_router_config().await?;
    validate_router_model_internal(&config, &model).await
        .map_err(|e| format!("Model validation failed: {}", e))
}

/// Execute Claude Code through the router
#[command]
pub async fn execute_claude_code_with_router(
    app: AppHandle,
    project_path: String,
    prompt: String,
    model: String,
    system_prompt: Option<String>,
) -> Result<(), String> {
    info!("Executing Claude Code with router - model: {}", model);
    
    // Check if router is available
    let router_available = check_router_availability().await?;
    
    if !router_available {
        warn!("Router not available, falling back to direct Claude CLI");
        return execute_claude_code_fallback(app, project_path, prompt, model, system_prompt).await;
    }
    
    // Validate model
    let config = get_router_config().await
        .map_err(|e| format!("Failed to get router config: {}", e))?;
    if !validate_router_model_internal(&config, &model).await
        .map_err(|e| format!("Model validation failed: {}", e))? {
        return Err(format!("Invalid model configuration: {}", model));
    }
    
    let cmd_args = build_router_command_args(&project_path, &prompt, &model, system_prompt.as_deref());
    
    let mut cmd = Command::new("ccr");
    for arg in cmd_args {
        cmd.arg(arg);
    }
    
    cmd.current_dir(&project_path);
    
    // Execute the command (this would integrate with existing streaming logic)
    spawn_router_process(app, cmd, prompt, model, project_path).await
}

// Internal helper functions

pub async fn load_router_config(config_path: &Path) -> Result<RouterConfig> {
    if !config_path.exists() {
        // Return default configuration if file doesn't exist
        return Ok(RouterConfig {
            openai_api_key: None,
            openai_base_url: None,
            openai_model: None,
            providers: vec![],
            router: None,
            use_plugins: None,
        });
    }
    
    let content = fs::read_to_string(config_path)
        .context("Failed to read router config file")?;
    
    let config: RouterConfig = serde_json::from_str(&content)
        .context("Failed to parse router config JSON")?;
    
    Ok(config)
}

pub async fn save_router_config(config_path: &Path, config: &RouterConfig) -> Result<()> {
    let json = serde_json::to_string_pretty(config)
        .context("Failed to serialize router config")?;
    
    fs::write(config_path, json)
        .context("Failed to write router config file")?;
    
    info!("Router configuration saved to: {}", config_path.display());
    Ok(())
}

pub fn get_available_models_from_config(config: &RouterConfig) -> Vec<String> {
    let mut models = Vec::new();
    
    // Add models from providers
    for provider in &config.providers {
        for model in &provider.models {
            models.push(format!("{},{}", provider.name, model));
        }
    }
    
    // Add default OpenAI model if configured
    if let (Some(model), Some(_)) = (&config.openai_model, &config.openai_api_key) {
        models.push(format!("openai,{}", model));
    }
    
    models.sort();
    models.dedup();
    models
}

pub async fn validate_router_model_internal(config: &RouterConfig, model: &str) -> Result<bool> {
    let parts: Vec<&str> = model.split(',').collect();
    if parts.len() != 2 {
        return Ok(false);
    }
    
    let (provider_name, model_name) = (parts[0], parts[1]);
    
    // Check if provider exists and has the model
    for provider in &config.providers {
        if provider.name == provider_name {
            return Ok(provider.models.contains(&model_name.to_string()));
        }
    }
    
    // Check default OpenAI configuration
    if provider_name == "openai" {
        if let Some(default_model) = &config.openai_model {
            return Ok(default_model == model_name);
        }
    }
    
    Ok(false)
}

pub fn build_router_command_args(
    _project_path: &str,
    prompt: &str,
    model: &str,
    system_prompt: Option<&str>,
) -> Vec<String> {
    let mut args = vec![
        "code".to_string(),
        "-p".to_string(),
        prompt.to_string(),
        "--model".to_string(),
        model.to_string(),
        "--output-format".to_string(),
        "stream-json".to_string(),
        "--verbose".to_string(),
        "--dangerously-skip-permissions".to_string(),
    ];
    
    if let Some(sys_prompt) = system_prompt {
        args.push("--system-prompt".to_string());
        args.push(sys_prompt.to_string());
    }
    
    args
}

pub fn build_fallback_command_args(
    _project_path: &str,
    prompt: &str,
    model: &str,
) -> Vec<String> {
    vec![
        "-p".to_string(),
        prompt.to_string(),
        "--model".to_string(),
        model.to_string(),
        "--output-format".to_string(),
        "stream-json".to_string(),
        "--verbose".to_string(),
        "--dangerously-skip-permissions".to_string(),
    ]
}

async fn execute_claude_code_fallback(
    app: AppHandle,
    project_path: String,
    prompt: String,
    model: String,
    _system_prompt: Option<String>,
) -> Result<(), String> {
    info!("Falling back to direct Claude CLI execution");
    
    // Use existing Claude CLI execution logic
    crate::commands::claude::execute_claude_code(app, project_path, prompt, model).await
}

async fn spawn_router_process(
    _app: AppHandle,
    _cmd: Command,
    _prompt: String,
    model: String,
    _project_path: String,
) -> Result<(), String> {
    // This would integrate with the existing process spawning logic
    // For now, we'll use the existing Claude process spawning as a template
    info!("Spawning router process for model: {}", model);
    
    // TODO: Implement router-specific process spawning
    // This should follow the same pattern as spawn_claude_process in claude.rs
    // but use the router command instead

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;
    use std::fs;
    use tempfile::tempdir;

    #[tokio::test]
    async fn test_router_config_creation() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("config.json");

        let config = RouterConfig {
            openai_api_key: Some("sk-test".to_string()),
            openai_base_url: Some("https://api.openai.com".to_string()),
            openai_model: Some("gpt-4".to_string()),
            providers: vec![
                Provider {
                    name: "openai".to_string(),
                    api_base_url: "https://api.openai.com".to_string(),
                    api_key: "sk-test".to_string(),
                    models: vec!["gpt-4".to_string(), "gpt-3.5-turbo".to_string()],
                }
            ],
            router: Some(RouterStrategy {
                background: Some("openai,gpt-3.5-turbo".to_string()),
                think: Some("openai,gpt-4".to_string()),
                long_context: Some("openai,gpt-4".to_string()),
            }),
            use_plugins: Some(vec!["toolcall-improvement".to_string()]),
        };

        let result = save_router_config(&config_path, &config).await;
        assert!(result.is_ok());
        assert!(config_path.exists());
    }

    #[tokio::test]
    async fn test_router_config_loading() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("config.json");

        let config_json = json!({
            "OPENAI_API_KEY": "sk-test",
            "OPENAI_BASE_URL": "https://api.openai.com",
            "OPENAI_MODEL": "gpt-4",
            "Providers": [
                {
                    "name": "openai",
                    "api_base_url": "https://api.openai.com",
                    "api_key": "sk-test",
                    "models": ["gpt-4", "gpt-3.5-turbo"]
                }
            ],
            "Router": {
                "background": "openai,gpt-3.5-turbo",
                "think": "openai,gpt-4",
                "longContext": "openai,gpt-4"
            },
            "usePlugins": ["toolcall-improvement"]
        });

        fs::write(&config_path, config_json.to_string()).unwrap();

        let result = load_router_config(&config_path).await;
        assert!(result.is_ok());

        let config = result.unwrap();
        assert_eq!(config.openai_api_key, Some("sk-test".to_string()));
        assert_eq!(config.providers.len(), 1);
        assert_eq!(config.providers[0].name, "openai");
    }

    #[tokio::test]
    async fn test_get_available_models() {
        let config = RouterConfig {
            openai_api_key: Some("sk-test".to_string()),
            openai_base_url: Some("https://api.openai.com".to_string()),
            openai_model: Some("gpt-4".to_string()),
            providers: vec![
                Provider {
                    name: "openai".to_string(),
                    api_base_url: "https://api.openai.com".to_string(),
                    api_key: "sk-test".to_string(),
                    models: vec!["gpt-4".to_string(), "gpt-3.5-turbo".to_string()],
                },
                Provider {
                    name: "anthropic".to_string(),
                    api_base_url: "https://api.anthropic.com".to_string(),
                    api_key: "sk-ant-test".to_string(),
                    models: vec!["claude-3.5-sonnet".to_string()],
                }
            ],
            router: None,
            use_plugins: None,
        };

        let models = get_available_models_from_config(&config);
        assert_eq!(models.len(), 3); // 3 from providers (deduped)
        assert!(models.contains(&"openai,gpt-4".to_string()));
        assert!(models.contains(&"openai,gpt-3.5-turbo".to_string()));
        assert!(models.contains(&"anthropic,claude-3.5-sonnet".to_string()));
    }

    #[tokio::test]
    async fn test_validate_router_model() {
        let config = RouterConfig {
            openai_api_key: Some("sk-test".to_string()),
            openai_base_url: None,
            openai_model: None,
            providers: vec![
                Provider {
                    name: "openai".to_string(),
                    api_base_url: "https://api.openai.com".to_string(),
                    api_key: "sk-test".to_string(),
                    models: vec!["gpt-4".to_string()],
                }
            ],
            router: None,
            use_plugins: None,
        };

        // Valid model
        let result = validate_router_model_internal(&config, "openai,gpt-4").await;
        assert!(result.is_ok());
        assert!(result.unwrap());

        // Invalid model
        let result = validate_router_model_internal(&config, "openai,gpt-5").await;
        assert!(result.is_ok());
        assert!(!result.unwrap());

        // Invalid provider
        let result = validate_router_model_internal(&config, "invalid,gpt-4").await;
        assert!(result.is_ok());
        assert!(!result.unwrap());
    }

    #[tokio::test]
    async fn test_router_command_building() {
        let project_path = "/test/project";
        let prompt = "Test prompt";
        let model = "openai,gpt-4";

        let cmd_args = build_router_command_args(project_path, prompt, model, None);

        assert!(cmd_args.contains(&"code".to_string()));
        assert!(cmd_args.contains(&"-p".to_string()));
        assert!(cmd_args.contains(&prompt.to_string()));
        assert!(cmd_args.contains(&"--model".to_string()));
        assert!(cmd_args.contains(&model.to_string()));
    }
}
