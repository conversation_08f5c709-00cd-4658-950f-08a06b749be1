import React, { useState, useEffect, useMemo } from "react";
import { Check, ChevronDown, AlertCircle, Settings } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { api, type RouterConfig, type RouterProvider } from "@/lib/api";
import { cn } from "@/lib/utils";

interface RouterModelSelectorProps {
  selectedModel: string;
  onModelChange: (model: string) => void;
  disabled?: boolean;
  validateModel?: boolean;
  className?: string;
}

interface ModelOption {
  id: string;
  label: string;
  provider: string;
  isRouter: boolean;
}

/**
 * Enhanced model selector that supports both Claude models and router models
 */
export const RouterModelSelector: React.FC<RouterModelSelectorProps> = ({
  selectedModel,
  onModelChange,
  disabled = false,
  validateModel = false,
  className
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [routerAvailable, setRouterAvailable] = useState(false);
  const [routerModels, setRouterModels] = useState<string[]>([]);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [showConfig, setShowConfig] = useState(false);

  // Default Claude models (fallback when router is not available)
  const defaultClaudeModels = [
    "claude-3.5-sonnet",
    "claude-3.5-haiku",
    "claude-3-opus",
    "claude-3-sonnet",
    "claude-3-haiku"
  ];

  const modelOptions = useMemo((): ModelOption[] => {
    const options: ModelOption[] = [];

    if (routerAvailable && routerModels.length > 0) {
      // Add router models
      routerModels.forEach(model => {
        const [provider, modelName] = model.split(',');
        options.push({
          id: model,
          label: formatModelLabel(provider, modelName),
          provider: provider,
          isRouter: true
        });
      });
    }

    // Add default Claude models
    defaultClaudeModels.forEach(model => {
      options.push({
        id: model,
        label: formatClaudeModelLabel(model),
        provider: "anthropic",
        isRouter: false
      });
    });

    return options;
  }, [routerAvailable, routerModels]);

  useEffect(() => {
    loadModels();
  }, []);

  useEffect(() => {
    if (validateModel && selectedModel) {
      validateSelectedModel();
    }
  }, [selectedModel, validateModel]);

  const loadModels = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check router availability
      const available = await api.checkRouterAvailability();
      setRouterAvailable(available);

      if (available) {
        // Load router models
        const models = await api.getAvailableRouterModels();
        setRouterModels(models);
      }
    } catch (err) {
      console.error("Failed to load models:", err);
      setError("Error loading models");
      setRouterAvailable(false);
    } finally {
      setIsLoading(false);
    }
  };

  const validateSelectedModel = async () => {
    try {
      setValidationError(null);
      
      if (selectedModel.includes(',')) {
        // Router model format
        const isValid = await api.validateRouterModel(selectedModel);
        if (!isValid) {
          setValidationError("Invalid model configuration");
        }
      }
    } catch (err) {
      console.error("Model validation failed:", err);
      setValidationError("Model validation failed");
    }
  };

  const formatModelLabel = (provider: string, modelName: string): string => {
    const providerLabels: Record<string, string> = {
      openai: "OpenAI",
      anthropic: "Anthropic",
      google: "Google",
      deepseek: "DeepSeek",
      ollama: "Ollama",
      openrouter: "OpenRouter"
    };

    const modelLabels: Record<string, string> = {
      "gpt-4": "GPT-4",
      "gpt-3.5-turbo": "GPT-3.5 Turbo",
      "claude-3.5-sonnet": "Claude 3.5 Sonnet",
      "claude-3.5-haiku": "Claude 3.5 Haiku",
      "gemini-2.5-pro-preview": "Gemini 2.5 Pro",
      "deepseek-reasoner": "DeepSeek Reasoner",
      "qwen2.5-coder:latest": "Qwen 2.5 Coder"
    };

    const providerLabel = providerLabels[provider] || provider;
    const modelLabel = modelLabels[modelName] || modelName;
    
    return `${modelLabel} (${providerLabel})`;
  };

  const formatClaudeModelLabel = (model: string): string => {
    const labels: Record<string, string> = {
      "claude-3.5-sonnet": "Claude 3.5 Sonnet",
      "claude-3.5-haiku": "Claude 3.5 Haiku",
      "claude-3-opus": "Claude 3 Opus",
      "claude-3-sonnet": "Claude 3 Sonnet",
      "claude-3-haiku": "Claude 3 Haiku"
    };
    
    return labels[model] || model;
  };

  const handleModelChange = (value: string) => {
    onModelChange(value);
    setValidationError(null);
  };

  const selectedOption = modelOptions.find(option => option.id === selectedModel);

  if (isLoading) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
        <span className="text-sm text-muted-foreground">Loading models...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex items-center space-x-2 text-red-600", className)}>
        <AlertCircle className="h-4 w-4" />
        <span className="text-sm">{error}</span>
        <Button variant="ghost" size="sm" onClick={loadModels}>
          Retry
        </Button>
      </div>
    );
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center space-x-2">
        <Select value={selectedModel} onValueChange={handleModelChange} disabled={disabled}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a model">
              {selectedOption ? selectedOption.label : selectedModel}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {routerAvailable && routerModels.length > 0 && (
              <>
                <div className="px-2 py-1 text-xs font-semibold text-muted-foreground">
                  Router Models
                </div>
                {modelOptions
                  .filter(option => option.isRouter)
                  .map(option => (
                    <SelectItem key={option.id} value={option.id}>
                      <div className="flex items-center space-x-2">
                        <span>{option.label}</span>
                        {option.id === selectedModel && <Check className="h-3 w-3" />}
                      </div>
                    </SelectItem>
                  ))}
                <div className="border-t my-1" />
              </>
            )}
            <div className="px-2 py-1 text-xs font-semibold text-muted-foreground">
              Claude Models
            </div>
            {modelOptions
              .filter(option => !option.isRouter)
              .map(option => (
                <SelectItem key={option.id} value={option.id}>
                  <div className="flex items-center space-x-2">
                    <span>{option.label}</span>
                    {option.id === selectedModel && <Check className="h-3 w-3" />}
                  </div>
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
        
        {routerAvailable && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowConfig(true)}
            title="Configure Router"
          >
            <Settings className="h-4 w-4" />
          </Button>
        )}
      </div>

      {validationError && (
        <div className="flex items-center space-x-2 text-red-600 text-sm">
          <AlertCircle className="h-3 w-3" />
          <span>{validationError}</span>
        </div>
      )}

      {!routerAvailable && (
        <div className="text-xs text-muted-foreground">
          Install claude-code-router for additional model options
        </div>
      )}

      <RouterConfigDialog
        open={showConfig}
        onSave={async (config) => {
          await api.saveRouterConfig(config);
          setShowConfig(false);
          loadModels(); // Reload models after config change
        }}
        onCancel={() => setShowConfig(false)}
      />
    </div>
  );
};

interface RouterConfigDialogProps {
  open: boolean;
  onSave: (config: RouterConfig) => void;
  onCancel: () => void;
}

/**
 * Dialog for configuring claude-code-router settings
 */
export const RouterConfigDialog: React.FC<RouterConfigDialogProps> = ({
  open,
  onSave,
  onCancel
}) => {
  const [config, setConfig] = useState<RouterConfig>({
    OPENAI_API_KEY: "",
    OPENAI_BASE_URL: "",
    OPENAI_MODEL: "",
    Providers: [],
    Router: undefined,
    usePlugins: []
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open) {
      loadConfig();
    }
  }, [open]);

  const loadConfig = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const existingConfig = await api.getRouterConfig();
      setConfig(existingConfig);
    } catch (err) {
      console.error("Failed to load router config:", err);
      setError("Failed to load configuration");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setIsLoading(true);
      setError(null);
      await onSave(config);
    } catch (err) {
      console.error("Failed to save router config:", err);
      setError("Failed to save configuration");
    } finally {
      setIsLoading(false);
    }
  };

  const addProvider = () => {
    setConfig(prev => ({
      ...prev,
      Providers: [
        ...prev.Providers,
        {
          name: "",
          api_base_url: "",
          api_key: "",
          models: []
        }
      ]
    }));
  };

  const updateProvider = (index: number, provider: RouterProvider) => {
    setConfig(prev => ({
      ...prev,
      Providers: prev.Providers.map((p, i) => i === index ? provider : p)
    }));
  };

  const removeProvider = (index: number) => {
    setConfig(prev => ({
      ...prev,
      Providers: prev.Providers.filter((_, i) => i !== index)
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Router Configuration</DialogTitle>
        </DialogHeader>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <div className="space-y-6">
          {/* OpenAI Default Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Default OpenAI Configuration</h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="openai-key">OpenAI API Key</Label>
                <Input
                  id="openai-key"
                  type="password"
                  value={config.OPENAI_API_KEY || ""}
                  onChange={(e) => setConfig(prev => ({ ...prev, OPENAI_API_KEY: e.target.value }))}
                  placeholder="sk-..."
                />
              </div>

              <div>
                <Label htmlFor="openai-url">OpenAI Base URL</Label>
                <Input
                  id="openai-url"
                  value={config.OPENAI_BASE_URL || ""}
                  onChange={(e) => setConfig(prev => ({ ...prev, OPENAI_BASE_URL: e.target.value }))}
                  placeholder="https://api.openai.com"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="openai-model">Default OpenAI Model</Label>
              <Input
                id="openai-model"
                value={config.OPENAI_MODEL || ""}
                onChange={(e) => setConfig(prev => ({ ...prev, OPENAI_MODEL: e.target.value }))}
                placeholder="gpt-4"
              />
            </div>
          </div>

          {/* Providers Configuration */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Providers</h3>
              <Button onClick={addProvider} variant="outline" size="sm">
                Add Provider
              </Button>
            </div>

            {config.Providers.map((provider, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Provider {index + 1}</h4>
                  <Button
                    onClick={() => removeProvider(index)}
                    variant="ghost"
                    size="sm"
                    className="text-red-600"
                  >
                    Remove
                  </Button>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Provider Name</Label>
                    <Input
                      value={provider.name}
                      onChange={(e) => updateProvider(index, { ...provider, name: e.target.value })}
                      placeholder="openai, anthropic, etc."
                    />
                  </div>

                  <div>
                    <Label>API Base URL</Label>
                    <Input
                      value={provider.api_base_url}
                      onChange={(e) => updateProvider(index, { ...provider, api_base_url: e.target.value })}
                      placeholder="https://api.provider.com"
                    />
                  </div>
                </div>

                <div>
                  <Label>API Key</Label>
                  <Input
                    type="password"
                    value={provider.api_key}
                    onChange={(e) => updateProvider(index, { ...provider, api_key: e.target.value })}
                    placeholder="API key for this provider"
                  />
                </div>

                <div>
                  <Label>Models (one per line)</Label>
                  <Textarea
                    value={provider.models.join('\n')}
                    onChange={(e) => updateProvider(index, {
                      ...provider,
                      models: e.target.value.split('\n').filter(m => m.trim())
                    })}
                    placeholder="gpt-4&#10;gpt-3.5-turbo"
                    rows={3}
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Plugins Configuration */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Plugins</h3>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Switch
                  checked={config.usePlugins?.includes("toolcall-improvement") || false}
                  onCheckedChange={(checked) => {
                    const plugins = config.usePlugins || [];
                    if (checked) {
                      setConfig(prev => ({
                        ...prev,
                        usePlugins: [...plugins.filter(p => p !== "toolcall-improvement"), "toolcall-improvement"]
                      }));
                    } else {
                      setConfig(prev => ({
                        ...prev,
                        usePlugins: plugins.filter(p => p !== "toolcall-improvement")
                      }));
                    }
                  }}
                />
                <Label>Toolcall Improvement</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  checked={config.usePlugins?.includes("notebook-tools-filter") || false}
                  onCheckedChange={(checked) => {
                    const plugins = config.usePlugins || [];
                    if (checked) {
                      setConfig(prev => ({
                        ...prev,
                        usePlugins: [...plugins.filter(p => p !== "notebook-tools-filter"), "notebook-tools-filter"]
                      }));
                    } else {
                      setConfig(prev => ({
                        ...prev,
                        usePlugins: plugins.filter(p => p !== "notebook-tools-filter")
                      }));
                    }
                  }}
                />
                <Label>Notebook Tools Filter</Label>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save Configuration"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
