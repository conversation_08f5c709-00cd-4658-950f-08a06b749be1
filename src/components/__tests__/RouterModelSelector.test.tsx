import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { RouterModelSelector } from '../RouterModelSelector';
import { api } from '@/lib/api';

// Mock the API
jest.mock('@/lib/api', () => ({
  api: {
    getRouterConfig: jest.fn(),
    getAvailableRouterModels: jest.fn(),
    validateRouterModel: jest.fn(),
    checkRouterAvailability: jest.fn(),
  },
}));

const mockApi = api as jest.Mocked<typeof api>;

describe('RouterModelSelector', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render model selector with default Claude models when router is not available', async () => {
    mockApi.checkRouterAvailability.mockResolvedValue(false);
    mockApi.getAvailableRouterModels.mockResolvedValue([]);

    const mockOnModelChange = jest.fn();
    
    render(
      <RouterModelSelector
        selectedModel="claude-3.5-sonnet"
        onModelChange={mockOnModelChange}
        disabled={false}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('claude-3.5-sonnet')).toBeInTheDocument();
    });

    expect(mockApi.checkRouterAvailability).toHaveBeenCalled();
  });

  it('should render router models when router is available', async () => {
    mockApi.checkRouterAvailability.mockResolvedValue(true);
    mockApi.getAvailableRouterModels.mockResolvedValue([
      'openai,gpt-4',
      'anthropic,claude-3.5-sonnet',
      'google,gemini-2.5-pro-preview'
    ]);

    const mockOnModelChange = jest.fn();
    
    render(
      <RouterModelSelector
        selectedModel="openai,gpt-4"
        onModelChange={mockOnModelChange}
        disabled={false}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('GPT-4 (OpenAI)')).toBeInTheDocument();
    });

    expect(mockApi.getAvailableRouterModels).toHaveBeenCalled();
  });

  it('should handle model selection change', async () => {
    mockApi.checkRouterAvailability.mockResolvedValue(true);
    mockApi.getAvailableRouterModels.mockResolvedValue([
      'openai,gpt-4',
      'anthropic,claude-3.5-sonnet'
    ]);

    const mockOnModelChange = jest.fn();
    
    render(
      <RouterModelSelector
        selectedModel="openai,gpt-4"
        onModelChange={mockOnModelChange}
        disabled={false}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('GPT-4 (OpenAI)')).toBeInTheDocument();
    });

    // Click to open dropdown
    fireEvent.click(screen.getByRole('combobox'));

    // Select different model
    await waitFor(() => {
      fireEvent.click(screen.getByText('Claude 3.5 Sonnet (Anthropic)'));
    });

    expect(mockOnModelChange).toHaveBeenCalledWith('anthropic,claude-3.5-sonnet');
  });

  it('should show loading state while fetching models', () => {
    mockApi.checkRouterAvailability.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    const mockOnModelChange = jest.fn();
    
    render(
      <RouterModelSelector
        selectedModel="claude-3.5-sonnet"
        onModelChange={mockOnModelChange}
        disabled={false}
      />
    );

    expect(screen.getByText('Loading models...')).toBeInTheDocument();
  });

  it('should handle error state gracefully', async () => {
    mockApi.checkRouterAvailability.mockRejectedValue(new Error('Router check failed'));
    
    const mockOnModelChange = jest.fn();
    
    render(
      <RouterModelSelector
        selectedModel="claude-3.5-sonnet"
        onModelChange={mockOnModelChange}
        disabled={false}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Error loading models')).toBeInTheDocument();
    });
  });

  it('should be disabled when disabled prop is true', async () => {
    mockApi.checkRouterAvailability.mockResolvedValue(false);
    
    const mockOnModelChange = jest.fn();
    
    render(
      <RouterModelSelector
        selectedModel="claude-3.5-sonnet"
        onModelChange={mockOnModelChange}
        disabled={true}
      />
    );

    await waitFor(() => {
      const selector = screen.getByRole('combobox');
      expect(selector).toBeDisabled();
    });
  });

  it('should validate model selection', async () => {
    mockApi.checkRouterAvailability.mockResolvedValue(true);
    mockApi.getAvailableRouterModels.mockResolvedValue(['openai,gpt-4']);
    mockApi.validateRouterModel.mockResolvedValue(true);

    const mockOnModelChange = jest.fn();
    
    render(
      <RouterModelSelector
        selectedModel="openai,gpt-4"
        onModelChange={mockOnModelChange}
        disabled={false}
        validateModel={true}
      />
    );

    await waitFor(() => {
      expect(mockApi.validateRouterModel).toHaveBeenCalledWith('openai,gpt-4');
    });
  });

  it('should show validation error for invalid model', async () => {
    mockApi.checkRouterAvailability.mockResolvedValue(true);
    mockApi.getAvailableRouterModels.mockResolvedValue(['openai,gpt-4']);
    mockApi.validateRouterModel.mockResolvedValue(false);

    const mockOnModelChange = jest.fn();
    
    render(
      <RouterModelSelector
        selectedModel="invalid,model"
        onModelChange={mockOnModelChange}
        disabled={false}
        validateModel={true}
      />
    );

    await waitFor(() => {
      expect(screen.getByText('Invalid model configuration')).toBeInTheDocument();
    });
  });
});

describe('RouterConfigDialog', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render router configuration dialog', () => {
    const mockOnSave = jest.fn();
    const mockOnCancel = jest.fn();
    
    render(
      <RouterConfigDialog
        open={true}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText('Router Configuration')).toBeInTheDocument();
    expect(screen.getByLabelText('OpenAI API Key')).toBeInTheDocument();
    expect(screen.getByLabelText('OpenAI Base URL')).toBeInTheDocument();
  });

  it('should handle form submission', async () => {
    mockApi.getRouterConfig.mockResolvedValue({
      openai_api_key: '',
      openai_base_url: '',
      openai_model: '',
      providers: [],
      router: null,
      use_plugins: null,
    });

    const mockOnSave = jest.fn();
    const mockOnCancel = jest.fn();
    
    render(
      <RouterConfigDialog
        open={true}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    );

    // Fill form
    fireEvent.change(screen.getByLabelText('OpenAI API Key'), {
      target: { value: 'sk-test-key' }
    });

    fireEvent.change(screen.getByLabelText('OpenAI Base URL'), {
      target: { value: 'https://api.openai.com' }
    });

    // Submit form
    fireEvent.click(screen.getByText('Save Configuration'));

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith(expect.objectContaining({
        openai_api_key: 'sk-test-key',
        openai_base_url: 'https://api.openai.com',
      }));
    });
  });

  it('should load existing configuration', async () => {
    const existingConfig = {
      openai_api_key: 'sk-existing',
      openai_base_url: 'https://api.openai.com',
      openai_model: 'gpt-4',
      providers: [],
      router: null,
      use_plugins: null,
    };

    mockApi.getRouterConfig.mockResolvedValue(existingConfig);

    const mockOnSave = jest.fn();
    const mockOnCancel = jest.fn();
    
    render(
      <RouterConfigDialog
        open={true}
        onSave={mockOnSave}
        onCancel={mockOnCancel}
      />
    );

    await waitFor(() => {
      expect(screen.getByDisplayValue('sk-existing')).toBeInTheDocument();
      expect(screen.getByDisplayValue('https://api.openai.com')).toBeInTheDocument();
    });
  });
});
