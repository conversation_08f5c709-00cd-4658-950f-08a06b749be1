import React, { useState, useEffect, useMemo } from "react";
import { Check, AlertCircle, <PERSON>tings, Zap, Brain, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  api, 
  type SuperClaudeCommand, 
  type SuperClaudePersona, 
  type SuperClaudeExecutionConfig,
  ThinkingDepth 
} from "@/lib/api";
import { cn } from "@/lib/utils";

interface SuperClaudeSelectorProps {
  selectedConfig: SuperClaudeExecutionConfig | null;
  onConfigChange: (config: SuperClaudeExecutionConfig | null) => void;
  disabled?: boolean;
  className?: string;
}

/**
 * SuperClaude configuration selector component
 */
export const SuperClaudeSelector: React.FC<SuperClaudeSelectorProps> = ({
  selectedConfig,
  onConfigChange,
  disabled = false,
  className
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [superClaudeAvailable, setSuperClaudeAvailable] = useState(false);
  const [commands, setCommands] = useState<SuperClaudeCommand[]>([]);
  const [personas, setPersonas] = useState<SuperClaudePersona[]>([]);
  const [showConfig, setShowConfig] = useState(false);

  useEffect(() => {
    loadSuperClaudeData();
  }, []);

  const loadSuperClaudeData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Check SuperClaude availability
      const available = await api.checkSuperClaudeAvailability();
      setSuperClaudeAvailable(available);

      if (available) {
        // Load commands and personas
        const [commandsData, personasData] = await Promise.all([
          api.getAvailableSuperClaudeCommands(),
          api.getAvailableSuperClaudePersonas()
        ]);
        setCommands(commandsData);
        setPersonas(personasData);
      }
    } catch (err) {
      console.error("Failed to load SuperClaude data:", err);
      setError("Error loading SuperClaude data");
      setSuperClaudeAvailable(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEnableSuperClaude = () => {
    const defaultConfig: SuperClaudeExecutionConfig = {
      command: "/analyze",
      flags: ["--code"],
      persona: "architect",
      mcp_servers: ["seq"],
      thinking_depth: ThinkingDepth.Standard,
      token_optimization: false,
    };
    onConfigChange(defaultConfig);
  };

  const handleDisableSuperClaude = () => {
    onConfigChange(null);
  };

  const handleConfigUpdate = (updates: Partial<SuperClaudeExecutionConfig>) => {
    if (selectedConfig) {
      onConfigChange({ ...selectedConfig, ...updates });
    }
  };

  const getCommandIcon = (command: string) => {
    if (command.includes("analyze") || command.includes("review")) return <Brain className="h-4 w-4" />;
    if (command.includes("build") || command.includes("dev")) return <Zap className="h-4 w-4" />;
    if (command.includes("scan") || command.includes("security")) return <Shield className="h-4 w-4" />;
    return <Settings className="h-4 w-4" />;
  };

  const getPersonaColor = (persona: string) => {
    const colors: Record<string, string> = {
      architect: "bg-blue-100 text-blue-800",
      frontend: "bg-green-100 text-green-800",
      backend: "bg-purple-100 text-purple-800",
      security: "bg-red-100 text-red-800",
      performance: "bg-orange-100 text-orange-800",
      qa: "bg-yellow-100 text-yellow-800",
    };
    return colors[persona] || "bg-gray-100 text-gray-800";
  };

  const getThinkingDepthInfo = (depth: ThinkingDepth) => {
    switch (depth) {
      case ThinkingDepth.Standard:
        return { label: "Standard", tokens: "~4K tokens", description: "Multi-file analysis" };
      case ThinkingDepth.Deep:
        return { label: "Deep", tokens: "~10K tokens", description: "Architecture-level depth" };
      case ThinkingDepth.Ultra:
        return { label: "Ultra", tokens: "~32K tokens", description: "Maximum depth analysis" };
    }
  };

  if (isLoading) {
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
        <span className="text-sm text-muted-foreground">Loading SuperClaude...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("flex items-center space-x-2 text-red-600", className)}>
        <AlertCircle className="h-4 w-4" />
        <span className="text-sm">{error}</span>
        <Button variant="ghost" size="sm" onClick={loadSuperClaudeData}>
          Retry
        </Button>
      </div>
    );
  }

  if (!superClaudeAvailable) {
    return (
      <div className={cn("space-y-2", className)}>
        <div className="flex items-center space-x-2 text-muted-foreground">
          <AlertCircle className="h-4 w-4" />
          <span className="text-sm">SuperClaude not available</span>
        </div>
        <div className="text-xs text-muted-foreground">
          Install SuperClaude framework for enhanced Claude Code capabilities
        </div>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Switch
            checked={selectedConfig !== null}
            onCheckedChange={(checked) => {
              if (checked) {
                handleEnableSuperClaude();
              } else {
                handleDisableSuperClaude();
              }
            }}
            disabled={disabled}
          />
          <Label className="text-sm font-medium">Enable SuperClaude</Label>
          <Badge variant="secondary" className="text-xs">
            Enhanced
          </Badge>
        </div>
        
        {selectedConfig && (
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowConfig(true)}
            title="Configure SuperClaude"
          >
            <Settings className="h-4 w-4" />
          </Button>
        )}
      </div>

      {selectedConfig && (
        <div className="space-y-3 p-3 border rounded-lg bg-muted/50">
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label className="text-xs text-muted-foreground">Command</Label>
              <Select
                value={selectedConfig.command}
                onValueChange={(value) => handleConfigUpdate({ command: value })}
                disabled={disabled}
              >
                <SelectTrigger className="h-8">
                  <SelectValue>
                    <div className="flex items-center space-x-2">
                      {getCommandIcon(selectedConfig.command)}
                      <span>{selectedConfig.command}</span>
                    </div>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  {commands.map((cmd) => (
                    <SelectItem key={cmd.name} value={cmd.name}>
                      <div className="flex items-center space-x-2">
                        {getCommandIcon(cmd.name)}
                        <span>{cmd.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs text-muted-foreground">Persona</Label>
              <Select
                value={selectedConfig.persona || ""}
                onValueChange={(value) => handleConfigUpdate({ persona: value || undefined })}
                disabled={disabled}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="Select persona">
                    {selectedConfig.persona && (
                      <Badge className={getPersonaColor(selectedConfig.persona)}>
                        {selectedConfig.persona}
                      </Badge>
                    )}
                  </SelectValue>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No persona</SelectItem>
                  {personas.map((persona) => (
                    <SelectItem key={persona.name} value={persona.name}>
                      <Badge className={getPersonaColor(persona.name)}>
                        {persona.name}
                      </Badge>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label className="text-xs text-muted-foreground">Thinking Depth</Label>
            <Select
              value={selectedConfig.thinking_depth}
              onValueChange={(value) => handleConfigUpdate({ thinking_depth: value as ThinkingDepth })}
              disabled={disabled}
            >
              <SelectTrigger className="h-8">
                <SelectValue>
                  {(() => {
                    const info = getThinkingDepthInfo(selectedConfig.thinking_depth);
                    return (
                      <div className="flex items-center justify-between w-full">
                        <span>{info.label}</span>
                        <span className="text-xs text-muted-foreground">{info.tokens}</span>
                      </div>
                    );
                  })()}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {Object.values(ThinkingDepth).map((depth) => {
                  const info = getThinkingDepthInfo(depth);
                  return (
                    <SelectItem key={depth} value={depth}>
                      <div className="flex items-center justify-between w-full">
                        <span>{info.label}</span>
                        <span className="text-xs text-muted-foreground">{info.tokens}</span>
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={selectedConfig.token_optimization}
              onCheckedChange={(checked) => handleConfigUpdate({ token_optimization: checked })}
              disabled={disabled}
            />
            <Label className="text-xs">Token optimization (UltraCompressed)</Label>
          </div>
        </div>
      )}

      <SuperClaudeConfigDialog
        open={showConfig}
        config={selectedConfig}
        commands={commands}
        personas={personas}
        onSave={(config) => {
          onConfigChange(config);
          setShowConfig(false);
        }}
        onCancel={() => setShowConfig(false)}
      />
    </div>
  );
};

interface SuperClaudeConfigDialogProps {
  open: boolean;
  config: SuperClaudeExecutionConfig | null;
  commands: SuperClaudeCommand[];
  personas: SuperClaudePersona[];
  onSave: (config: SuperClaudeExecutionConfig) => void;
  onCancel: () => void;
}

/**
 * Dialog for advanced SuperClaude configuration
 */
export const SuperClaudeConfigDialog: React.FC<SuperClaudeConfigDialogProps> = ({
  open,
  config,
  commands,
  personas,
  onSave,
  onCancel
}) => {
  const [localConfig, setLocalConfig] = useState<SuperClaudeExecutionConfig>(
    config || {
      command: "/analyze",
      flags: ["--code"],
      persona: "architect",
      mcp_servers: ["seq"],
      thinking_depth: ThinkingDepth.Standard,
      token_optimization: false,
    }
  );

  useEffect(() => {
    if (config) {
      setLocalConfig(config);
    }
  }, [config]);

  const handleSave = () => {
    onSave(localConfig);
  };

  const commandsByCategory = useMemo(() => {
    const grouped: Record<string, SuperClaudeCommand[]> = {};
    commands.forEach(cmd => {
      if (!grouped[cmd.category]) {
        grouped[cmd.category] = [];
      }
      grouped[cmd.category].push(cmd);
    });
    return grouped;
  }, [commands]);

  const availableFlags = [
    "--think", "--think-hard", "--ultrathink",
    "--seq", "--magic", "--c7", "--pup",
    "--validate", "--security", "--coverage", "--strict",
    "--plan", "--dry-run", "--watch", "--interactive",
    "--uc", "--no-mcp"
  ];

  const availableMcpServers = [
    { id: "seq", name: "Sequential", description: "Sequential thinking analysis" },
    { id: "magic", name: "Magic", description: "UI component generation" },
    { id: "c7", name: "Context7", description: "Documentation lookup" },
    { id: "pup", name: "Puppeteer", description: "Browser automation" },
  ];

  return (
    <Dialog open={open} onOpenChange={onCancel}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>SuperClaude Configuration</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="command" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="command">Command</TabsTrigger>
            <TabsTrigger value="persona">Persona</TabsTrigger>
            <TabsTrigger value="flags">Flags</TabsTrigger>
            <TabsTrigger value="mcp">MCP Servers</TabsTrigger>
          </TabsList>

          <TabsContent value="command" className="space-y-4">
            <div>
              <Label>Select Command</Label>
              <div className="grid grid-cols-1 gap-2 mt-2">
                {Object.entries(commandsByCategory).map(([category, cmds]) => (
                  <div key={category}>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">{category}</h4>
                    <div className="grid grid-cols-2 gap-2">
                      {cmds.map((cmd) => (
                        <div
                          key={cmd.name}
                          className={cn(
                            "p-3 border rounded-lg cursor-pointer transition-colors",
                            localConfig.command === cmd.name
                              ? "border-primary bg-primary/10"
                              : "border-muted hover:border-muted-foreground/50"
                          )}
                          onClick={() => setLocalConfig(prev => ({ ...prev, command: cmd.name }))}
                        >
                          <div className="flex items-center space-x-2 mb-1">
                            <span className="font-medium text-sm">{cmd.name}</span>
                            {localConfig.command === cmd.name && <Check className="h-3 w-3" />}
                          </div>
                          <p className="text-xs text-muted-foreground">{cmd.description}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="persona" className="space-y-4">
            <div>
              <Label>Select Persona</Label>
              <div className="grid grid-cols-2 gap-3 mt-2">
                <div
                  className={cn(
                    "p-3 border rounded-lg cursor-pointer transition-colors",
                    !localConfig.persona
                      ? "border-primary bg-primary/10"
                      : "border-muted hover:border-muted-foreground/50"
                  )}
                  onClick={() => setLocalConfig(prev => ({ ...prev, persona: undefined }))}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-sm">No Persona</span>
                    {!localConfig.persona && <Check className="h-3 w-3" />}
                  </div>
                  <p className="text-xs text-muted-foreground">Use default Claude behavior</p>
                </div>
                {personas.map((persona) => (
                  <div
                    key={persona.name}
                    className={cn(
                      "p-3 border rounded-lg cursor-pointer transition-colors",
                      localConfig.persona === persona.name
                        ? "border-primary bg-primary/10"
                        : "border-muted hover:border-muted-foreground/50"
                    )}
                    onClick={() => setLocalConfig(prev => ({ ...prev, persona: persona.name }))}
                  >
                    <div className="flex items-center space-x-2 mb-1">
                      <Badge className={cn("text-xs", localConfig.persona === persona.name ? "bg-primary text-primary-foreground" : "")}>
                        {persona.name}
                      </Badge>
                      {localConfig.persona === persona.name && <Check className="h-3 w-3" />}
                    </div>
                    <p className="text-xs text-muted-foreground mb-1">{persona.description}</p>
                    <p className="text-xs text-muted-foreground font-medium">Best for: {persona.best_for}</p>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="flags" className="space-y-4">
            <div>
              <Label>Additional Flags</Label>
              <div className="grid grid-cols-3 gap-2 mt-2">
                {availableFlags.map((flag) => (
                  <div key={flag} className="flex items-center space-x-2">
                    <Switch
                      checked={localConfig.flags.includes(flag)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setLocalConfig(prev => ({
                            ...prev,
                            flags: [...prev.flags.filter(f => f !== flag), flag]
                          }));
                        } else {
                          setLocalConfig(prev => ({
                            ...prev,
                            flags: prev.flags.filter(f => f !== flag)
                          }));
                        }
                      }}
                    />
                    <Label className="text-sm">{flag}</Label>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="mcp" className="space-y-4">
            <div>
              <Label>MCP Servers</Label>
              <div className="grid grid-cols-2 gap-3 mt-2">
                {availableMcpServers.map((server) => (
                  <div key={server.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                    <Switch
                      checked={localConfig.mcp_servers.includes(server.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setLocalConfig(prev => ({
                            ...prev,
                            mcp_servers: [...prev.mcp_servers.filter(s => s !== server.id), server.id]
                          }));
                        } else {
                          setLocalConfig(prev => ({
                            ...prev,
                            mcp_servers: prev.mcp_servers.filter(s => s !== server.id)
                          }));
                        }
                      }}
                    />
                    <div>
                      <div className="font-medium text-sm">{server.name}</div>
                      <div className="text-xs text-muted-foreground">{server.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button onClick={handleSave}>
            Save Configuration
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
